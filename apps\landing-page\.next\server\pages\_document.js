"use strict";(()=>{var e={};e.id=660,e.ids=[660],e.modules={479:(e,t,n)=>{n.r(t),n.d(t,{default:()=>Document});var a=n(997),s=n(331);function Document(){return(0,a.jsxs)(s.Html,{children:[(0,a.jsxs)(s.<PERSON>,{children:[a.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),a.jsx("link",{rel:"icon",href:"/favicon.ico"}),a.jsx("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),a.jsx("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),a.jsx("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),a.jsx("link",{rel:"manifest",href:"/site.webmanifest"}),a.jsx("meta",{name:"theme-color",content:"#0ea5e9"}),a.jsx("meta",{name:"msapplication-TileColor",content:"#0ea5e9"}),a.jsx("meta",{property:"og:type",content:"website"}),a.jsx("meta",{property:"og:site_name",content:"Freela Syria"}),a.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),a.jsx("meta",{name:"twitter:site",content:"@freela_syria"}),a.jsx("meta",{name:"format-detection",content:"telephone=no"}),a.jsx("meta",{name:"mobile-web-app-capable",content:"yes"}),a.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),a.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"})]}),(0,a.jsxs)("body",{className:"antialiased",children:[a.jsx(s.Main,{}),a.jsx(s.NextScript,{})]})]})}},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{e.exports=require("react")},997:e=>{e.exports=require("react/jsx-runtime")},1017:e=>{e.exports=require("path")}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),a=t.X(0,[217,331],()=>n(479));module.exports=a})();