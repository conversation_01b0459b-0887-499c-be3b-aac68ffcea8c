// Theme system TypeScript interfaces and types

export type ThemeName = 'gold' | 'purple';

export interface ColorPalette {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  950?: string;
}

export interface GlassEffectConfig {
  background: string;
  border: string;
  shadow: string;
  backdropBlur: string;
}

export interface BackgroundConfig {
  primary: string;
  secondary: string;
  tertiary?: string;
  pattern?: string;
}

export interface TextColorConfig {
  primary: string;
  secondary: string;
  accent: string;
  muted: string;
}

export interface GradientConfig {
  primary: string;
  secondary: string;
  accent: string;
  text: string;
  button: string;
  card: string;
}

export interface ShadowConfig {
  sm: string;
  md: string;
  lg: string;
  xl: string;
  glass: string;
  premium: string;
}

export interface AnimationConfig {
  shimmer: string;
  glow: string;
  pulse: string;
  float: string;
}

export interface ThemeConfig {
  name: ThemeName;
  displayName: string;
  colors: {
    primary: ColorPalette;
    secondary: ColorPalette;
    accent: ColorPalette;
    neutral: ColorPalette;
    glass: GlassEffectConfig;
    text: TextColorConfig;
  };
  backgrounds: BackgroundConfig;
  gradients: GradientConfig;
  shadows: ShadowConfig;
  animations: AnimationConfig;
}

export interface ThemeContextType {
  currentTheme: ThemeConfig;
  themeName: ThemeName;
  switchTheme: (theme: ThemeName) => void;
  isGoldTheme: boolean;
  isPurpleTheme: boolean;
  themeClasses: string;
}

export interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: ThemeName;
}

// CSS Custom Property Names
export interface CSSCustomProperties {
  // Primary colors
  '--theme-primary-50': string;
  '--theme-primary-100': string;
  '--theme-primary-200': string;
  '--theme-primary-300': string;
  '--theme-primary-400': string;
  '--theme-primary-500': string;
  '--theme-primary-600': string;
  '--theme-primary-700': string;
  '--theme-primary-800': string;
  '--theme-primary-900': string;
  
  // Glass effects
  '--theme-glass-bg': string;
  '--theme-glass-border': string;
  '--theme-glass-shadow': string;
  '--theme-glass-blur': string;
  
  // Backgrounds
  '--theme-bg-primary': string;
  '--theme-bg-secondary': string;
  '--theme-bg-tertiary': string;
  
  // Text colors
  '--theme-text-primary': string;
  '--theme-text-secondary': string;
  '--theme-text-accent': string;
  '--theme-text-muted': string;
  
  // Gradients
  '--theme-gradient-primary': string;
  '--theme-gradient-secondary': string;
  '--theme-gradient-accent': string;
  '--theme-gradient-text': string;
  '--theme-gradient-button': string;
  '--theme-gradient-card': string;
  
  // Shadows
  '--theme-shadow-sm': string;
  '--theme-shadow-md': string;
  '--theme-shadow-lg': string;
  '--theme-shadow-xl': string;
  '--theme-shadow-glass': string;
  '--theme-shadow-premium': string;
}

// Utility types for theme manipulation
export type ThemeColorKey = keyof ThemeConfig['colors'];
export type ThemeGradientKey = keyof GradientConfig;
export type ThemeShadowKey = keyof ShadowConfig;

// Theme switching animation options
export interface ThemeSwitchOptions {
  duration?: number;
  easing?: string;
  preserveScroll?: boolean;
}

// Theme controller configuration
export interface ThemeControllerConfig {
  position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  showInProduction: boolean;
  keyboardShortcut: string;
  persistTheme: boolean;
}
