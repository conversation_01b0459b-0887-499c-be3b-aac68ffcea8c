'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@/themes';
import {
  SwatchIcon,
  SparklesIcon,
  CommandLineIcon,
  XMarkIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';

interface ThemeControllerProps {
  showInProduction?: boolean;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

export const ThemeController: React.FC<ThemeControllerProps> = ({
  showInProduction = false,
  position = 'bottom-right'
}) => {
  const { switchTheme, isGoldTheme, isPurpleTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // Check if we should show the controller
  useEffect(() => {
    const isDevelopment = process.env.NODE_ENV === 'development';
    setIsVisible(isDevelopment || showInProduction);
  }, [showInProduction]);

  // Don't render in production unless explicitly enabled
  if (!isVisible) return null;

  // Position classes
  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-6 right-6',
    'top-left': 'top-6 left-6',
  };

  const handleThemeSwitch = (theme: 'gold' | 'purple') => {
    switchTheme(theme);
    // Auto-close after selection
    setTimeout(() => setIsOpen(false), 500);
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-[9999] select-none`}>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
            className="mb-4 p-4 rounded-2xl overflow-hidden"
            style={{
              background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%)',
              backdropFilter: 'blur(25px)',
              WebkitBackdropFilter: 'blur(25px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              boxShadow: '0 25px 50px rgba(0, 0, 0, 0.5)',
            }}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <SwatchIcon className="w-5 h-5 text-white" />
                <span className="text-white font-semibold text-sm">Theme Controller</span>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="text-white/60 hover:text-white transition-colors p-1"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            </div>

            {/* Theme Options */}
            <div className="space-y-3">
              {/* Gold Theme */}
              <motion.button
                onClick={() => handleThemeSwitch('gold')}
                className={`w-full p-3 rounded-xl transition-all duration-300 group ${
                  isGoldTheme 
                    ? 'ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-500/20 to-orange-500/20' 
                    : 'hover:bg-white/5'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center gap-3">
                  {/* Gold Preview */}
                  <div className="relative">
                    <div 
                      className="w-8 h-8 rounded-lg overflow-hidden"
                      style={{
                        background: 'linear-gradient(135deg, #FFD700 0%, #B8860B 100%)',
                        boxShadow: '0 4px 12px rgba(255, 215, 0, 0.3)',
                      }}
                    />
                    {isGoldTheme && (
                      <div className="absolute -top-1 -right-1">
                        <SparklesIcon className="w-4 h-4 text-yellow-400" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 text-left">
                    <div className="text-white font-medium text-sm">Gold Premium</div>
                    <div className="text-white/60 text-xs">Luxury & Elegance</div>
                  </div>
                  
                  {isGoldTheme && (
                    <div className="text-yellow-400">
                      <CommandLineIcon className="w-4 h-4" />
                    </div>
                  )}
                </div>
              </motion.button>

              {/* Purple Theme */}
              <motion.button
                onClick={() => handleThemeSwitch('purple')}
                className={`w-full p-3 rounded-xl transition-all duration-300 group ${
                  isPurpleTheme 
                    ? 'ring-2 ring-purple-400 bg-gradient-to-r from-purple-500/20 to-blue-500/20' 
                    : 'hover:bg-white/5'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center gap-3">
                  {/* Purple Preview */}
                  <div className="relative">
                    <div 
                      className="w-8 h-8 rounded-lg overflow-hidden"
                      style={{
                        background: 'linear-gradient(135deg, #d946ef 0%, #a21caf 100%)',
                        boxShadow: '0 4px 12px rgba(217, 70, 239, 0.3)',
                      }}
                    />
                    {isPurpleTheme && (
                      <div className="absolute -top-1 -right-1">
                        <SparklesIcon className="w-4 h-4 text-purple-400" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 text-left">
                    <div className="text-white font-medium text-sm">Purple Dark</div>
                    <div className="text-white/60 text-xs">Modern & Professional</div>
                  </div>
                  
                  {isPurpleTheme && (
                    <div className="text-purple-400">
                      <CommandLineIcon className="w-4 h-4" />
                    </div>
                  )}
                </div>
              </motion.button>
            </div>

            {/* Keyboard Shortcut Info */}
            <div className="mt-4 pt-3 border-t border-white/10">
              <div className="text-white/40 text-xs text-center">
                Press <kbd className="px-1 py-0.5 bg-white/10 rounded text-white/60">Ctrl+Shift+T</kbd> to toggle
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Toggle Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-3 rounded-full overflow-hidden group"
        style={{
          background: isGoldTheme 
            ? 'linear-gradient(135deg, #FFD700 0%, #B8860B 100%)'
            : 'linear-gradient(135deg, #d946ef 0%, #a21caf 100%)',
          boxShadow: isGoldTheme
            ? '0 8px 25px rgba(255, 215, 0, 0.4)'
            : '0 8px 25px rgba(217, 70, 239, 0.4)',
        }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        animate={{
          rotate: isOpen ? 180 : 0,
        }}
        transition={{ duration: 0.3 }}
      >
        {/* Background Shimmer Effect */}
        <div 
          className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          style={{
            background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)',
            backgroundSize: '200% 100%',
            animation: 'shimmer 2s ease-in-out infinite',
          }}
        />
        
        {/* Icon */}
        <div className="relative z-10">
          {isOpen ? (
            <ChevronDownIcon className="w-6 h-6 text-white" />
          ) : (
            <SwatchIcon className="w-6 h-6 text-white" />
          )}
        </div>

        {/* Active Theme Indicator */}
        <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-white shadow-lg" />
      </motion.button>

      {/* Floating Label */}
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -10 }}
            className="absolute right-full mr-3 top-1/2 -translate-y-1/2 px-2 py-1 rounded-md text-xs text-white whitespace-nowrap pointer-events-none"
            style={{
              background: 'rgba(0, 0, 0, 0.8)',
              backdropFilter: 'blur(10px)',
            }}
          >
            {isGoldTheme ? 'Gold Theme' : 'Purple Theme'}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ThemeController;
