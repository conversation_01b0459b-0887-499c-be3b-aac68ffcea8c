'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { ThemeContextType, ThemeProviderProps, ThemeName, ThemeConfig } from './types';
import {
  getTheme,
  applyThemeToDocument,
  storeTheme,
  getThemeClasses,
  enableThemeTransitions,
  injectThemeCSS,
  initializeTheme
} from './theme-utils';

// Create theme context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme provider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  defaultTheme = 'gold' 
}) => {
  const [themeName, setThemeName] = useState<ThemeName>(defaultTheme);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize theme on mount
  useEffect(() => {
    const initialTheme = initializeTheme(defaultTheme);
    setThemeName(initialTheme);
    setIsInitialized(true);
  }, [defaultTheme]);

  // Get current theme configuration
  const currentTheme: ThemeConfig = getTheme(themeName);

  // Switch theme function
  const switchTheme = useCallback((newTheme: ThemeName) => {
    if (newTheme === themeName) return;

    // Enable smooth transitions
    enableThemeTransitions();

    // Apply new theme
    applyThemeToDocument(newTheme);
    injectThemeCSS(newTheme);

    // Update state
    setThemeName(newTheme);

    // Store in localStorage
    storeTheme(newTheme);

    // Dispatch custom event for other components
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('themeChanged', { 
        detail: { theme: newTheme } 
      }));
    }
  }, [themeName]);

  // Keyboard shortcut handler
  useEffect(() => {
    const handleKeyboard = (event: KeyboardEvent) => {
      // Ctrl+Shift+T to toggle theme
      if (event.ctrlKey && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        const newTheme = themeName === 'gold' ? 'purple' : 'gold';
        switchTheme(newTheme);
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('keydown', handleKeyboard);
      return () => window.removeEventListener('keydown', handleKeyboard);
    }
  }, [themeName, switchTheme]);

  // Context value
  const contextValue: ThemeContextType = {
    currentTheme,
    themeName,
    switchTheme,
    isGoldTheme: themeName === 'gold',
    isPurpleTheme: themeName === 'purple',
    themeClasses: getThemeClasses(themeName),
  };

  // Don't render until initialized to prevent hydration mismatch
  if (!isInitialized) {
    return null;
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      <div className={`theme-provider ${getThemeClasses(themeName)}`}>
        {children}
      </div>
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme context
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
};

// Higher-order component for theme-aware components
export const withTheme = <P extends object>(
  Component: React.ComponentType<P & { theme: ThemeContextType }>
) => {
  const ThemedComponent = (props: P) => {
    const theme = useTheme();
    return <Component {...props} theme={theme} />;
  };
  
  ThemedComponent.displayName = `withTheme(${Component.displayName || Component.name})`;
  return ThemedComponent;
};

// Theme-aware component wrapper
export const ThemeAware: React.FC<{
  children: (theme: ThemeContextType) => React.ReactNode;
}> = ({ children }) => {
  const theme = useTheme();
  return <>{children(theme)}</>;
};

// Export theme utilities for direct use
export {
  getTheme,
  getThemeClasses,
  applyThemeToDocument,
  storeTheme,
} from './theme-utils';
export { getStoredTheme } from './theme-utils';

// Export theme configurations
export { goldTheme } from './gold-theme';
export { purpleTheme } from './purple-theme';

// Export types
export type { ThemeName, ThemeConfig, ThemeContextType } from './types';
