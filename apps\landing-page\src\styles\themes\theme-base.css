/* Base theme utilities and animations */

/* Shimmer animation keyframes */
@keyframes shimmer {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Theme transition utilities */
.theme-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-transition-fast {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-transition-slow {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base glass effect utilities */
.glass-base {
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Theme-aware text utilities */
.text-theme-primary {
  color: var(--theme-text-primary);
}

.text-theme-secondary {
  color: var(--theme-text-secondary);
}

.text-theme-accent {
  color: var(--theme-text-accent);
}

.text-theme-muted {
  color: var(--theme-text-muted);
}

/* Theme-aware background utilities */
.bg-theme-primary {
  background: var(--theme-bg-primary);
}

.bg-theme-secondary {
  background: var(--theme-bg-secondary);
}

.bg-theme-tertiary {
  background: var(--theme-bg-tertiary);
}

/* Theme-aware gradient utilities */
.bg-theme-gradient-primary {
  background: var(--theme-gradient-primary);
}

.bg-theme-gradient-secondary {
  background: var(--theme-gradient-secondary);
}

.bg-theme-gradient-accent {
  background: var(--theme-gradient-accent);
}

.bg-theme-gradient-card {
  background: var(--theme-gradient-card);
}

/* Theme-aware text gradient utilities */
.text-theme-gradient {
  background: var(--theme-gradient-text);
  background-size: 300% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer 4s ease-in-out infinite;
}

/* Theme-aware glass effect utilities */
.glass-theme {
  background: var(--theme-glass-bg);
  border: 1px solid var(--theme-glass-border);
  box-shadow: var(--theme-shadow-glass);
  backdrop-filter: var(--theme-glass-blur);
  -webkit-backdrop-filter: var(--theme-glass-blur);
}

/* Theme-aware shadow utilities */
.shadow-theme-sm {
  box-shadow: var(--theme-shadow-sm);
}

.shadow-theme-md {
  box-shadow: var(--theme-shadow-md);
}

.shadow-theme-lg {
  box-shadow: var(--theme-shadow-lg);
}

.shadow-theme-xl {
  box-shadow: var(--theme-shadow-xl);
}

.shadow-theme-premium {
  box-shadow: var(--theme-shadow-premium);
}

/* Theme-aware button utilities */
.btn-theme-primary {
  background: var(--theme-gradient-button);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-glass-border);
  box-shadow: var(--theme-shadow-premium);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}

.btn-theme-primary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--theme-shadow-premium), 0 0 30px var(--theme-glass-bg);
}

.btn-theme-secondary {
  background: var(--theme-gradient-card);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-glass-border);
  box-shadow: var(--theme-shadow-lg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}

.btn-theme-secondary:hover {
  background: var(--theme-glass-bg);
  transform: translateY(-1px);
}

/* Theme-aware card utilities */
.card-theme {
  background: var(--theme-gradient-card);
  border: 1px solid var(--theme-glass-border);
  box-shadow: var(--theme-shadow-lg);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  transition: all 0.3s ease;
}

.card-theme:hover {
  transform: translateY(-4px);
  box-shadow: var(--theme-shadow-premium);
}

/* Theme-aware input utilities */
.input-theme {
  background: var(--theme-gradient-card);
  border: 1px solid var(--theme-glass-border);
  color: var(--theme-text-primary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.input-theme:focus {
  border-color: var(--theme-text-accent);
  box-shadow: 0 0 0 3px rgba(var(--theme-primary-500), 0.1);
}

.input-theme::placeholder {
  color: var(--theme-text-muted);
}

/* Theme-aware border utilities */
.border-theme {
  border-color: var(--theme-glass-border);
}

.border-theme-accent {
  border-color: var(--theme-text-accent);
}

/* Theme-aware hover effects */
.hover-theme-glow:hover {
  box-shadow: 0 0 30px var(--theme-glass-bg);
}

.hover-theme-lift:hover {
  transform: translateY(-2px);
}

.hover-theme-scale:hover {
  transform: scale(1.02);
}

/* Theme-aware focus effects */
.focus-theme:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--theme-glass-bg);
}

/* Theme-aware selection */
.theme-selection::selection {
  background: var(--theme-glass-bg);
  color: var(--theme-text-primary);
}

/* Responsive theme utilities */
@media (max-width: 768px) {
  .glass-theme {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }
  
  .text-theme-gradient {
    background-size: 200% 100%;
  }
}

/* Print styles - disable theme effects */
@media print {
  .glass-theme,
  .card-theme,
  .btn-theme-primary,
  .btn-theme-secondary {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }
  
  .text-theme-gradient {
    background: none !important;
    -webkit-background-clip: unset !important;
    background-clip: unset !important;
    -webkit-text-fill-color: black !important;
    color: black !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .text-theme-gradient {
    animation: none;
  }
  
  .theme-transition,
  .theme-transition-fast,
  .theme-transition-slow {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-theme {
    border-width: 2px;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
  
  .text-theme-gradient {
    background: none;
    -webkit-background-clip: unset;
    background-clip: unset;
    -webkit-text-fill-color: var(--theme-text-accent);
    color: var(--theme-text-accent);
  }
}
