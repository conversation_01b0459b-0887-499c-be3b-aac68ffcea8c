import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useState, useEffect } from 'react';
import { StarIcon } from '@heroicons/react/24/solid';
import { useTheme } from '../../themes';

export default function Testimonials() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const { isGoldTheme } = useTheme();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Get testimonials from translation
  const testimonials = Array.from({ length: 3 }, (_, i) => ({
    name: t(`testimonials.items.${i}.name`),
    role: t(`testimonials.items.${i}.role`),
    location: t(`testimonials.items.${i}.location`),
    content: t(`testimonials.items.${i}.content`),
    rating: parseInt(t(`testimonials.items.${i}.rating`)),
  }));

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`w-5 h-5 ${
          i < rating
            ? (isGoldTheme ? 'text-gold-400' : 'text-purple-400')
            : 'text-gray-300 dark:text-gray-600'
        }`}
      />
    ));
  };

  // Theme-aware background
  const getThemeBackground = () => {
    if (isGoldTheme) {
      return `
        radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(255, 215, 0, 0.06) 0%, transparent 60%),
        linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
      `;
    } else {
      return `
        radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(217, 70, 239, 0.06) 0%, transparent 60%),
        linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
      `;
    }
  };

  return (
    <section
      id="testimonials"
      ref={ref}
      className="relative section-padding overflow-hidden"
      style={{
        background: getThemeBackground()
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Theme-aware Glass Orbs */}
        <motion.div
          animate={{
            y: [-18, 18, -18],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 17, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-28 left-8 w-24 h-24 rounded-full opacity-15"
          style={{
            background: isGoldTheme ? 'rgba(255, 215, 0, 0.1)' : 'rgba(217, 70, 239, 0.1)',
            backdropFilter: 'blur(18px)',
            WebkitBackdropFilter: 'blur(18px)',
            border: isGoldTheme ? '1px solid rgba(255, 215, 0, 0.2)' : '1px solid rgba(217, 70, 239, 0.2)',
            boxShadow: isGoldTheme ? '0 8px 32px rgba(255, 215, 0, 0.1)' : '0 8px 32px rgba(217, 70, 239, 0.1)'
          }}
        />

        <motion.div
          animate={{
            y: [25, -25, 25],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 23, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-32 right-12 w-32 h-32 rounded-full opacity-12"
          style={{
            background: isGoldTheme ? 'rgba(184, 134, 11, 0.1)' : 'rgba(162, 28, 175, 0.1)',
            backdropFilter: 'blur(22px)',
            WebkitBackdropFilter: 'blur(22px)',
            border: isGoldTheme ? '1px solid rgba(184, 134, 11, 0.2)' : '1px solid rgba(162, 28, 175, 0.2)',
            boxShadow: isGoldTheme ? '0 8px 32px rgba(184, 134, 11, 0.1)' : '0 8px 32px rgba(162, 28, 175, 0.1)'
          }}
        />

        {/* Theme-aware Floating Particles */}
        {[...Array(7)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-14, 14, -14],
              x: [-7, 7, -7],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 6 + i * 1.4,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.7
            }}
            className={`absolute w-1.5 h-1.5 rounded-full ${isGoldTheme ? 'bg-gold-400/30' : 'bg-purple-400/30'}`}
            style={{
              left: `${18 + (i * 11)}%`,
              top: `${25 + (i * 9)}%`,
              filter: 'blur(0.5px)'
            }}
          />
        ))}
      </div>
      <div className="container mx-auto container-padding relative z-10">
        {/* Enhanced Section Header with Glass Effect */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mb-20"
        >
          <motion.div
            variants={itemVariants}
            className="relative mb-8"
          >
            <motion.h2
              className={`heading-lg mb-6 relative z-10 px-8 py-4 text-arabic-premium ${isGoldTheme ? 'text-theme-gradient' : 'text-white'}`}
              initial={{ opacity: 0, y: 30, filter: 'blur(10px)' }}
              animate={{ opacity: 1, y: 0, filter: 'blur(0px)' }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
            >
              {t('testimonials.title')}
            </motion.h2>

            {/* Glass backdrop for title */}
            <div
              className="absolute inset-0 -m-4 rounded-2xl opacity-25"
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%)',
                backdropFilter: 'blur(30px)',
                WebkitBackdropFilter: 'blur(30px)',
                border: '1px solid rgba(255, 255, 255, 0.12)',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.08)'
              }}
            />
          </motion.div>

          <motion.p
            variants={itemVariants}
            className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed text-arabic px-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {t('testimonials.subtitle')}
          </motion.p>
        </motion.div>

        {/* Enhanced Testimonials Grid with Glass Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20"
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                duration: 0.6,
                delay: 0.6 + (index * 0.15),
                type: 'spring',
                stiffness: 100
              }}
              whileHover={{
                scale: 1.05,
                y: -8,
                transition: { duration: 0.2 }
              }}
              className="card-premium p-8 relative overflow-hidden group cursor-pointer"
            >
              {/* Enhanced Quote Icon with Glass Effect */}
              <div className="absolute top-4 right-4 opacity-15 group-hover:opacity-30 transition-opacity duration-300">
                <div className="w-12 h-12 rounded-xl flex items-center justify-center"
                  style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                >
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
                  </svg>
                </div>
              </div>

              {/* Enhanced Rating with Syrian Colors */}
              <motion.div
                className="flex items-center mb-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 + (index * 0.1) }}
              >
                {renderStars(testimonial.rating)}
              </motion.div>

              {/* Enhanced Content */}
              <motion.blockquote
                className="text-white/90 mb-8 leading-relaxed text-lg text-arabic"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.9 + (index * 0.1) }}
              >
                &ldquo;{testimonial.content}&rdquo;
              </motion.blockquote>

              {/* Enhanced Author Section */}
              <motion.div
                className="flex items-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.0 + (index * 0.1) }}
              >
                {/* Enhanced Avatar with Glass Effect */}
                <div className="w-14 h-14 rounded-2xl flex items-center justify-center text-white font-bold text-xl me-4 relative overflow-hidden"
                  style={{
                    background: isGoldTheme
                      ? (index % 3 === 0
                        ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(255, 215, 0, 0.2) 100%)'
                        : index % 3 === 1
                        ? 'linear-gradient(135deg, rgba(184, 134, 11, 0.3) 0%, rgba(184, 134, 11, 0.2) 100%)'
                        : 'linear-gradient(135deg, rgba(218, 165, 32, 0.3) 0%, rgba(218, 165, 32, 0.2) 100%)')
                      : (index % 3 === 0
                        ? 'linear-gradient(135deg, rgba(217, 70, 239, 0.3) 0%, rgba(217, 70, 239, 0.2) 100%)'
                        : index % 3 === 1
                        ? 'linear-gradient(135deg, rgba(162, 28, 175, 0.3) 0%, rgba(162, 28, 175, 0.2) 100%)'
                        : 'linear-gradient(135deg, rgba(139, 69, 19, 0.3) 0%, rgba(139, 69, 19, 0.2) 100%)'),
                    backdropFilter: 'blur(15px)',
                    WebkitBackdropFilter: 'blur(15px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                >
                  {testimonial.name.charAt(0)}

                  {/* Shimmer effect */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                    style={{
                      background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)',
                      backgroundSize: '200% 100%',
                      animation: 'glassShimmer 2s ease-in-out infinite'
                    }}
                  />
                </div>

                <div>
                  <div className="font-bold text-white text-arabic-premium mb-1">
                    {testimonial.name}
                  </div>
                  <div className="text-sm text-white/70 text-arabic">
                    {testimonial.role} • {testimonial.location}
                  </div>
                </div>
              </motion.div>

              {/* Enhanced Decorative Element with Theme Colors */}
              <div className={`absolute bottom-0 left-0 w-full h-1 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ${isGoldTheme ? 'bg-gradient-to-r from-gold-500 via-gold-600 to-gold-400' : 'bg-gradient-to-r from-purple-500 via-purple-600 to-purple-400'}`} />
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Stats Section with Glass Effect */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center"
        >
          <div className="card-premium p-12 lg:p-16 relative overflow-hidden">
            {/* Gold accent elements */}
            <div className="absolute top-6 left-6 flex space-x-1 rtl:space-x-reverse opacity-20">
              <div className="w-2 h-12 bg-gold-500 rounded"></div>
              <div className="w-2 h-12 bg-gold-600 rounded"></div>
            </div>

            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <motion.div
                className="text-center group"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <motion.div
                  className="text-5xl lg:text-6xl font-black text-white mb-4 font-cairo"
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.6, type: 'spring', stiffness: 100 }}
                >
                  98%
                </motion.div>
                <div className="text-white/80 text-lg font-semibold text-arabic">
                  {isRTL ? 'معدل الرضا' : 'Satisfaction Rate'}
                </div>
              </motion.div>

              <motion.div
                className="text-center group"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <motion.div
                  className="text-5xl lg:text-6xl font-black text-white mb-4 font-cairo"
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.8, type: 'spring', stiffness: 100 }}
                >
                  4.9/5
                </motion.div>
                <div className="text-white/80 text-lg font-semibold text-arabic">
                  {isRTL ? 'متوسط التقييم' : 'Average Rating'}
                </div>
              </motion.div>

              <motion.div
                className="text-center group"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <motion.div
                  className="text-5xl lg:text-6xl font-black text-white mb-4 font-cairo"
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.0, type: 'spring', stiffness: 100 }}
                >
                  24/7
                </motion.div>
                <div className="text-white/80 text-lg font-semibold text-arabic">
                  {isRTL ? 'الدعم الفني' : 'Support Available'}
                </div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
