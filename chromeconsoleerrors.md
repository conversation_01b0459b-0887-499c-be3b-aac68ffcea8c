serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 0 was removed.
background.js:2 Uncaught (in promise) Error: Could not establish connection. Receiving end does not exist.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
serviceWorker.js:1 Uncaught (in promise) Error: Frame with ID 810 was removed.
Unchecked runtime.lastError: No tab with id: 789786023.
Unchecked runtime.lastError: No tab with id: 789786023.
Unchecked runtime.lastError: No tab with id: 789786023.
background.js:2 Uncaught (in promise) Error: The page keeping the extension port is moved into back/forward cache, so the message channel is closed.
background.js:2 Uncaught (in promise) Error: The page keeping the extension port is moved into back/forward cache, so the message channel is closed.
background.js:2 Uncaught (in promise) Error: The page keeping the extension port is moved into back/forward cache, so the message channel is closed.
background.js:2 Uncaught (in promise) Error: No tab with id: 789786343.
background.js:2 Uncaught (in promise) Error: Could not establish connection. Receiving end does not exist.
serviceWorker.js:1 Uncaught (in promise) Error: No tab with id: 789786348.
serviceWorker.js:1 Uncaught (in promise) Error: No tab with id: 789786348.
contentscript.bundle.js:1 Uncaught (in promise) Object
client.js:2 Warning: validateDOMNesting(...): <a> cannot appear as a descendant of <a>.
    at a
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at a
    at LinkComponent (webpack-internal:///../../node_modules/next/dist/client/link.js:105:19)
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at div
    at nav
    at header
    at Header (webpack-internal:///./src/components/Layout/Header.tsx:30:88)
    at div
    at Layout (webpack-internal:///./src/components/Layout/index.tsx:13:11)
    at HomePage (webpack-internal:///./src/pages/index.tsx:38:79)
    at f (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:597)
    at $ (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:348)
    at div
    at ThemeProvider (webpack-internal:///./src/themes/index.tsx:30:11)
    at div
    at ErrorBoundary (webpack-internal:///./src/components/ErrorBoundary.tsx:82:9)
    at App (webpack-internal:///./src/pages/_app.tsx:45:11)
    at I18nextProvider (webpack-internal:///../../node_modules/react-i18next/dist/es/I18nextProvider.js:11:5)
    at AppWithTranslation (webpack-internal:///../../node_modules/next-i18next/dist/esm/appWithTranslation.js:50:22)
    at PathnameContextProviderAdapter (webpack-internal:///../../node_modules/next/dist/shared/lib/router/adapters.js:79:11)
    at ErrorBoundary (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:5391)
    at ReactDevOverlay (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:7787)
    at Container (webpack-internal:///../../node_modules/next/dist/client/index.js:79:1)
    at AppContainer (webpack-internal:///../../node_modules/next/dist/client/index.js:208:11)
    at Root (webpack-internal:///../../node_modules/next/dist/client/index.js:422:11)
console.error @ client.js:2
localhost/:1 Error while trying to use the following icon from the Manifest: http://localhost:3001/android-chrome-192x192.png (Download error or resource isn't a valid image)
client.js:2 Warning: Updating a style property during rerender (background) when a conflicting property is set (backgroundClip) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at span
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at a
    at LinkComponent (webpack-internal:///../../node_modules/next/dist/client/link.js:105:19)
    at div
    at nav
    at header
    at Header (webpack-internal:///./src/components/Layout/Header.tsx:30:88)
    at div
    at Layout (webpack-internal:///./src/components/Layout/index.tsx:13:11)
    at HomePage (webpack-internal:///./src/pages/index.tsx:38:79)
    at f (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:597)
    at $ (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:348)
    at div
    at ThemeProvider (webpack-internal:///./src/themes/index.tsx:30:11)
    at div
    at ErrorBoundary (webpack-internal:///./src/components/ErrorBoundary.tsx:82:9)
    at App (webpack-internal:///./src/pages/_app.tsx:45:11)
    at I18nextProvider (webpack-internal:///../../node_modules/react-i18next/dist/es/I18nextProvider.js:11:5)
    at AppWithTranslation (webpack-internal:///../../node_modules/next-i18next/dist/esm/appWithTranslation.js:50:22)
    at PathnameContextProviderAdapter (webpack-internal:///../../node_modules/next/dist/shared/lib/router/adapters.js:79:11)
    at ErrorBoundary (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:5391)
    at ReactDevOverlay (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:7787)
    at Container (webpack-internal:///../../node_modules/next/dist/client/index.js:79:1)
    at AppContainer (webpack-internal:///../../node_modules/next/dist/client/index.js:208:11)
    at Root (webpack-internal:///../../node_modules/next/dist/client/index.js:422:11)
console.error @ client.js:2
client.js:2 Warning: Updating a style property during rerender (background) when a conflicting property is set (backgroundClip) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at h2
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at section
    at HowItWorks (webpack-internal:///./src/components/sections/HowItWorks.tsx:27:79)
    at main
    at main
    at div
    at Layout (webpack-internal:///./src/components/Layout/index.tsx:13:11)
    at HomePage (webpack-internal:///./src/pages/index.tsx:38:79)
    at f (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:597)
    at $ (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:348)
    at div
    at ThemeProvider (webpack-internal:///./src/themes/index.tsx:30:11)
    at div
    at ErrorBoundary (webpack-internal:///./src/components/ErrorBoundary.tsx:82:9)
    at App (webpack-internal:///./src/pages/_app.tsx:45:11)
    at I18nextProvider (webpack-internal:///../../node_modules/react-i18next/dist/es/I18nextProvider.js:11:5)
    at AppWithTranslation (webpack-internal:///../../node_modules/next-i18next/dist/esm/appWithTranslation.js:50:22)
    at PathnameContextProviderAdapter (webpack-internal:///../../node_modules/next/dist/shared/lib/router/adapters.js:79:11)
    at ErrorBoundary (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:5391)
    at ReactDevOverlay (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:7787)
    at Container (webpack-internal:///../../node_modules/next/dist/client/index.js:79:1)
    at AppContainer (webpack-internal:///../../node_modules/next/dist/client/index.js:208:11)
    at Root (webpack-internal:///../../node_modules/next/dist/client/index.js:422:11)
console.error @ client.js:2
client.js:2 Warning: Updating a style property during rerender (background) when a conflicting property is set (backgroundClip) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at h2
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at section
    at Pricing (webpack-internal:///./src/components/sections/Pricing.tsx:27:79)
    at main
    at main
    at div
    at Layout (webpack-internal:///./src/components/Layout/index.tsx:13:11)
    at HomePage (webpack-internal:///./src/pages/index.tsx:38:79)
    at f (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:597)
    at $ (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:348)
    at div
    at ThemeProvider (webpack-internal:///./src/themes/index.tsx:30:11)
    at div
    at ErrorBoundary (webpack-internal:///./src/components/ErrorBoundary.tsx:82:9)
    at App (webpack-internal:///./src/pages/_app.tsx:45:11)
    at I18nextProvider (webpack-internal:///../../node_modules/react-i18next/dist/es/I18nextProvider.js:11:5)
    at AppWithTranslation (webpack-internal:///../../node_modules/next-i18next/dist/esm/appWithTranslation.js:50:22)
    at PathnameContextProviderAdapter (webpack-internal:///../../node_modules/next/dist/shared/lib/router/adapters.js:79:11)
    at ErrorBoundary (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:5391)
    at ReactDevOverlay (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:7787)
    at Container (webpack-internal:///../../node_modules/next/dist/client/index.js:79:1)
    at AppContainer (webpack-internal:///../../node_modules/next/dist/client/index.js:208:11)
    at Root (webpack-internal:///../../node_modules/next/dist/client/index.js:422:11)
console.error @ client.js:2
client.js:2 Warning: Updating a style property during rerender (background) when a conflicting property is set (backgroundClip) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at h2
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at section
    at About (webpack-internal:///./src/components/sections/About.tsx:29:79)
    at main
    at main
    at div
    at Layout (webpack-internal:///./src/components/Layout/index.tsx:13:11)
    at HomePage (webpack-internal:///./src/pages/index.tsx:38:79)
    at f (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:597)
    at $ (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:348)
    at div
    at ThemeProvider (webpack-internal:///./src/themes/index.tsx:30:11)
    at div
    at ErrorBoundary (webpack-internal:///./src/components/ErrorBoundary.tsx:82:9)
    at App (webpack-internal:///./src/pages/_app.tsx:45:11)
    at I18nextProvider (webpack-internal:///../../node_modules/react-i18next/dist/es/I18nextProvider.js:11:5)
    at AppWithTranslation (webpack-internal:///../../node_modules/next-i18next/dist/esm/appWithTranslation.js:50:22)
    at PathnameContextProviderAdapter (webpack-internal:///../../node_modules/next/dist/shared/lib/router/adapters.js:79:11)
    at ErrorBoundary (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:5391)
    at ReactDevOverlay (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:7787)
    at Container (webpack-internal:///../../node_modules/next/dist/client/index.js:79:1)
    at AppContainer (webpack-internal:///../../node_modules/next/dist/client/index.js:208:11)
    at Root (webpack-internal:///../../node_modules/next/dist/client/index.js:422:11)
console.error @ client.js:2
client.js:2 Warning: Updating a style property during rerender (background) when a conflicting property is set (backgroundClip) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at h3
    at div
    at div
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at section
    at About (webpack-internal:///./src/components/sections/About.tsx:29:79)
    at main
    at main
    at div
    at Layout (webpack-internal:///./src/components/Layout/index.tsx:13:11)
    at HomePage (webpack-internal:///./src/pages/index.tsx:38:79)
    at f (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:597)
    at $ (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:348)
    at div
    at ThemeProvider (webpack-internal:///./src/themes/index.tsx:30:11)
    at div
    at ErrorBoundary (webpack-internal:///./src/components/ErrorBoundary.tsx:82:9)
    at App (webpack-internal:///./src/pages/_app.tsx:45:11)
    at I18nextProvider (webpack-internal:///../../node_modules/react-i18next/dist/es/I18nextProvider.js:11:5)
    at AppWithTranslation (webpack-internal:///../../node_modules/next-i18next/dist/esm/appWithTranslation.js:50:22)
    at PathnameContextProviderAdapter (webpack-internal:///../../node_modules/next/dist/shared/lib/router/adapters.js:79:11)
    at ErrorBoundary (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:5391)
    at ReactDevOverlay (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:7787)
    at Container (webpack-internal:///../../node_modules/next/dist/client/index.js:79:1)
    at AppContainer (webpack-internal:///../../node_modules/next/dist/client/index.js:208:11)
    at Root (webpack-internal:///../../node_modules/next/dist/client/index.js:422:11)
console.error @ client.js:2
client.js:2 Warning: Updating a style property during rerender (background) when a conflicting property is set (backgroundClip) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at h2
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at section
    at Contact (webpack-internal:///./src/components/sections/Contact.tsx:43:79)
    at main
    at main
    at div
    at Layout (webpack-internal:///./src/components/Layout/index.tsx:13:11)
    at HomePage (webpack-internal:///./src/pages/index.tsx:38:79)
    at f (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:597)
    at $ (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:348)
    at div
    at ThemeProvider (webpack-internal:///./src/themes/index.tsx:30:11)
    at div
    at ErrorBoundary (webpack-internal:///./src/components/ErrorBoundary.tsx:82:9)
    at App (webpack-internal:///./src/pages/_app.tsx:45:11)
    at I18nextProvider (webpack-internal:///../../node_modules/react-i18next/dist/es/I18nextProvider.js:11:5)
    at AppWithTranslation (webpack-internal:///../../node_modules/next-i18next/dist/esm/appWithTranslation.js:50:22)
    at PathnameContextProviderAdapter (webpack-internal:///../../node_modules/next/dist/shared/lib/router/adapters.js:79:11)
    at ErrorBoundary (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:5391)
    at ReactDevOverlay (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:7787)
    at Container (webpack-internal:///../../node_modules/next/dist/client/index.js:79:1)
    at AppContainer (webpack-internal:///../../node_modules/next/dist/client/index.js:208:11)
    at Root (webpack-internal:///../../node_modules/next/dist/client/index.js:422:11)
console.error @ client.js:2
client.js:2 Warning: Updating a style property during rerender (background) when a conflicting property is set (backgroundClip) can lead to styling bugs. To avoid this, don't mix shorthand and non-shorthand properties for the same value; instead, replace the shorthand with separate values.
    at span
    at a
    at LinkComponent (webpack-internal:///../../node_modules/next/dist/client/link.js:105:19)
    at div
    at MotionComponent (webpack-internal:///../../node_modules/framer-motion/dist/es/motion/index.mjs:49:65)
    at div
    at div
    at div
    at div
    at footer
    at Footer (webpack-internal:///./src/components/Layout/Footer.tsx:28:79)
    at div
    at Layout (webpack-internal:///./src/components/Layout/index.tsx:13:11)
    at HomePage (webpack-internal:///./src/pages/index.tsx:38:79)
    at f (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:597)
    at $ (webpack-internal:///../../node_modules/next-themes/dist/index.module.js:8:348)
    at div
    at ThemeProvider (webpack-internal:///./src/themes/index.tsx:30:11)
    at div
    at ErrorBoundary (webpack-internal:///./src/components/ErrorBoundary.tsx:82:9)
    at App (webpack-internal:///./src/pages/_app.tsx:45:11)
    at I18nextProvider (webpack-internal:///../../node_modules/react-i18next/dist/es/I18nextProvider.js:11:5)
    at AppWithTranslation (webpack-internal:///../../node_modules/next-i18next/dist/esm/appWithTranslation.js:50:22)
    at PathnameContextProviderAdapter (webpack-internal:///../../node_modules/next/dist/shared/lib/router/adapters.js:79:11)
    at ErrorBoundary (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:5391)
    at ReactDevOverlay (webpack-internal:///../../node_modules/next/dist/compiled/@next/react-dev-overlay/dist/client.js:2:7787)
    at Container (webpack-internal:///../../node_modules/next/dist/client/index.js:79:1)
    at AppContainer (webpack-internal:///../../node_modules/next/dist/client/index.js:208:11)
    at Root (webpack-internal:///../../node_modules/next/dist/client/index.js:422:11)
console.error @ client.js:2
