import { ThemeConfig } from './types';

export const purpleTheme: ThemeConfig = {
  name: 'purple',
  displayName: 'Purple Dark',
  colors: {
    primary: {
      50: '#fdf4ff',
      100: '#fae8ff',
      200: '#f5d0fe',
      300: '#f0abfc',
      400: '#e879f9',
      500: '#d946ef',
      600: '#c026d3',
      700: '#a21caf',
      800: '#86198f',
      900: '#701a75',
      950: '#4a044e',
    },
    secondary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
      950: '#082f49',
    },
    accent: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554',
    },
    neutral: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717',
      950: '#0a0a0a',
    },
    glass: {
      background: 'rgba(217, 70, 239, 0.12)',
      border: 'rgba(217, 70, 239, 0.25)',
      shadow: '0 8px 32px rgba(217, 70, 239, 0.15)',
      backdropBlur: 'blur(25px)',
    },
    text: {
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.8)',
      accent: '#d946ef',
      muted: 'rgba(255, 255, 255, 0.6)',
    },
  },
  backgrounds: {
    primary: `
      radial-gradient(circle at 50% 50%, rgba(217, 70, 239, 0.08) 0%, transparent 60%),
      linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%)
    `,
    secondary: `
      radial-gradient(ellipse at top, rgba(192, 38, 211, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
    `,
    tertiary: `
      radial-gradient(ellipse at bottom, rgba(162, 28, 175, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)
    `,
  },
  gradients: {
    primary: 'linear-gradient(135deg, #d946ef 0%, #a21caf 100%)',
    secondary: 'linear-gradient(135deg, #c026d3 0%, #86198f 100%)',
    accent: 'linear-gradient(135deg, #e879f9 0%, #c026d3 100%)',
    text: `linear-gradient(90deg,
      #701a75 0%, #86198f 10%, #a21caf 20%, #c026d3 30%,
      #d946ef 40%, #e879f9 50%, #d946ef 60%, #c026d3 70%,
      #a21caf 80%, #86198f 90%, #701a75 100%)`,
    button: 'linear-gradient(135deg, #d946ef 0%, #a21caf 50%, #86198f 100%)',
    card: 'linear-gradient(135deg, rgba(217, 70, 239, 0.08) 0%, rgba(217, 70, 239, 0.04) 100%)',
  },
  shadows: {
    sm: '0 2px 4px rgba(217, 70, 239, 0.1)',
    md: '0 4px 8px rgba(217, 70, 239, 0.15)',
    lg: '0 8px 16px rgba(217, 70, 239, 0.2)',
    xl: '0 12px 24px rgba(217, 70, 239, 0.25)',
    glass: '0 8px 32px rgba(217, 70, 239, 0.15)',
    premium: '0 25px 50px rgba(217, 70, 239, 0.3)',
  },
  animations: {
    shimmer: `
      @keyframes purpleShimmer {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
      }
    `,
    glow: `
      @keyframes purpleGlow {
        0%, 100% { box-shadow: 0 0 20px rgba(217, 70, 239, 0.3); }
        50% { box-shadow: 0 0 40px rgba(217, 70, 239, 0.6); }
      }
    `,
    pulse: `
      @keyframes purplePulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
    `,
    float: `
      @keyframes purpleFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
    `,
  },
};

// Purple theme CSS custom properties
export const purpleThemeCSSProperties = {
  // Primary colors
  '--theme-primary-50': purpleTheme.colors.primary[50],
  '--theme-primary-100': purpleTheme.colors.primary[100],
  '--theme-primary-200': purpleTheme.colors.primary[200],
  '--theme-primary-300': purpleTheme.colors.primary[300],
  '--theme-primary-400': purpleTheme.colors.primary[400],
  '--theme-primary-500': purpleTheme.colors.primary[500],
  '--theme-primary-600': purpleTheme.colors.primary[600],
  '--theme-primary-700': purpleTheme.colors.primary[700],
  '--theme-primary-800': purpleTheme.colors.primary[800],
  '--theme-primary-900': purpleTheme.colors.primary[900],
  
  // Glass effects
  '--theme-glass-bg': purpleTheme.colors.glass.background,
  '--theme-glass-border': purpleTheme.colors.glass.border,
  '--theme-glass-shadow': purpleTheme.colors.glass.shadow,
  '--theme-glass-blur': purpleTheme.colors.glass.backdropBlur,
  
  // Backgrounds
  '--theme-bg-primary': purpleTheme.backgrounds.primary,
  '--theme-bg-secondary': purpleTheme.backgrounds.secondary,
  '--theme-bg-tertiary': purpleTheme.backgrounds.tertiary || purpleTheme.backgrounds.secondary,
  
  // Text colors
  '--theme-text-primary': purpleTheme.colors.text.primary,
  '--theme-text-secondary': purpleTheme.colors.text.secondary,
  '--theme-text-accent': purpleTheme.colors.text.accent,
  '--theme-text-muted': purpleTheme.colors.text.muted,
  
  // Gradients
  '--theme-gradient-primary': purpleTheme.gradients.primary,
  '--theme-gradient-secondary': purpleTheme.gradients.secondary,
  '--theme-gradient-accent': purpleTheme.gradients.accent,
  '--theme-gradient-text': purpleTheme.gradients.text,
  '--theme-gradient-button': purpleTheme.gradients.button,
  '--theme-gradient-card': purpleTheme.gradients.card,
  
  // Shadows
  '--theme-shadow-sm': purpleTheme.shadows.sm,
  '--theme-shadow-md': purpleTheme.shadows.md,
  '--theme-shadow-lg': purpleTheme.shadows.lg,
  '--theme-shadow-xl': purpleTheme.shadows.xl,
  '--theme-shadow-glass': purpleTheme.shadows.glass,
  '--theme-shadow-premium': purpleTheme.shadows.premium,
};
