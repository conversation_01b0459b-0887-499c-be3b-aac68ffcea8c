{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "locale": false, "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/ar/home", "destination": "/", "statusCode": 308, "regex": "^(?!/_next)/ar/home(?:/)?$"}, {"source": "/:nextInternalLocale(ar|en)/home", "destination": "/:nextInternalLocale", "statusCode": 308, "regex": "^(?!/_next)(?:/(ar|en))/home(?:/)?$"}], "headers": [{"source": "/:nextInternalLocale(ar|en)/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}], "regex": "^(?:/(ar|en))(?:/(.*))(?:/)?$"}], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}], "dataRoutes": [{"page": "/", "dataRouteRegex": "^/_next/data/sP3wifsnu7y_U83ypJ8OS/index.json$"}], "i18n": {"defaultLocale": "ar", "locales": ["ar", "en"], "localeDetection": false}, "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}