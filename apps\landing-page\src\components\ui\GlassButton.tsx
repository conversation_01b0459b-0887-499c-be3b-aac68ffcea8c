import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface GlassButtonProps {
  children: ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'glass';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  href?: string;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
}

export default function GlassButton({ 
  children, 
  className = '', 
  variant = 'glass',
  size = 'md',
  onClick,
  href,
  type = 'button',
  disabled = false
}: GlassButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-semibold transition-all duration-300 relative overflow-hidden group';
  
  const variantClasses = {
    primary: 'btn-primary',
    secondary: 'btn-secondary', 
    glass: 'btn-glass'
  };

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm rounded-lg',
    md: 'px-6 py-3 text-base rounded-xl',
    lg: 'px-8 py-4 text-lg rounded-xl'
  };

  const hoverProps = {
    whileHover: { 
      scale: 1.02, 
      y: -1,
      transition: { duration: 0.2 }
    },
    whileTap: { scale: 0.98 }
  };

  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`;

  if (href) {
    return (
      <motion.a
        href={href}
        className={buttonClasses}
        {...(!disabled && hoverProps)}
      >
        {children}
        {/* Shimmer effect */}
        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
        </div>
      </motion.a>
    );
  }

  return (
    <motion.button
      type={type}
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
      {...(!disabled && hoverProps)}
    >
      {children}
      {/* Shimmer effect */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
      </div>
    </motion.button>
  );
}
