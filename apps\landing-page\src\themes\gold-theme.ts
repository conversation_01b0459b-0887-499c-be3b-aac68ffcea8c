import { ThemeConfig } from './types';

export const goldTheme: ThemeConfig = {
  name: 'gold',
  displayName: 'Gold Premium',
  colors: {
    primary: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
      950: '#451a03',
    },
    secondary: {
      50: '#fefce8',
      100: '#fef9c3',
      200: '#fef08a',
      300: '#fde047',
      400: '#facc15',
      500: '#eab308',
      600: '#ca8a04',
      700: '#a16207',
      800: '#854d0e',
      900: '#713f12',
      950: '#422006',
    },
    accent: {
      50: '#fff7ed',
      100: '#ffedd5',
      200: '#fed7aa',
      300: '#fdba74',
      400: '#fb923c',
      500: '#f97316',
      600: '#ea580c',
      700: '#c2410c',
      800: '#9a3412',
      900: '#7c2d12',
      950: '#431407',
    },
    neutral: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717',
      950: '#0a0a0a',
    },
    glass: {
      background: 'rgba(255, 215, 0, 0.12)',
      border: 'rgba(255, 215, 0, 0.25)',
      shadow: '0 8px 32px rgba(255, 215, 0, 0.15)',
      backdropBlur: 'blur(25px)',
    },
    text: {
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.8)',
      accent: '#FFD700',
      muted: 'rgba(255, 255, 255, 0.6)',
    },
  },
  backgrounds: {
    primary: `
      radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.08) 0%, transparent 60%),
      linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%)
    `,
    secondary: `
      radial-gradient(ellipse at top, rgba(184, 134, 11, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
    `,
    tertiary: `
      radial-gradient(ellipse at bottom, rgba(139, 105, 20, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)
    `,
  },
  gradients: {
    primary: 'linear-gradient(135deg, #FFD700 0%, #B8860B 100%)',
    secondary: 'linear-gradient(135deg, #FFA500 0%, #FF8C00 100%)',
    accent: 'linear-gradient(135deg, #DAA520 0%, #B8860B 100%)',
    text: `linear-gradient(90deg,
      #8B6914 0%, #B8860B 10%, #DAA520 20%, #FFD700 30%,
      #FFED4E 40%, #FFF8DC 50%, #FFED4E 60%, #FFD700 70%,
      #DAA520 80%, #B8860B 90%, #8B6914 100%)`,
    button: 'linear-gradient(135deg, #FFD700 0%, #B8860B 50%, #DAA520 100%)',
    card: 'linear-gradient(135deg, rgba(255, 215, 0, 0.08) 0%, rgba(255, 215, 0, 0.04) 100%)',
  },
  shadows: {
    sm: '0 2px 4px rgba(255, 215, 0, 0.1)',
    md: '0 4px 8px rgba(255, 215, 0, 0.15)',
    lg: '0 8px 16px rgba(255, 215, 0, 0.2)',
    xl: '0 12px 24px rgba(255, 215, 0, 0.25)',
    glass: '0 8px 32px rgba(255, 215, 0, 0.15)',
    premium: '0 25px 50px rgba(255, 215, 0, 0.3)',
  },
  animations: {
    shimmer: `
      @keyframes goldShimmer {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
      }
    `,
    glow: `
      @keyframes goldGlow {
        0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
        50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }
      }
    `,
    pulse: `
      @keyframes goldPulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
    `,
    float: `
      @keyframes goldFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
    `,
  },
};

// Gold theme CSS custom properties
export const goldThemeCSSProperties = {
  // Primary colors
  '--theme-primary-50': goldTheme.colors.primary[50],
  '--theme-primary-100': goldTheme.colors.primary[100],
  '--theme-primary-200': goldTheme.colors.primary[200],
  '--theme-primary-300': goldTheme.colors.primary[300],
  '--theme-primary-400': goldTheme.colors.primary[400],
  '--theme-primary-500': goldTheme.colors.primary[500],
  '--theme-primary-600': goldTheme.colors.primary[600],
  '--theme-primary-700': goldTheme.colors.primary[700],
  '--theme-primary-800': goldTheme.colors.primary[800],
  '--theme-primary-900': goldTheme.colors.primary[900],
  
  // Glass effects
  '--theme-glass-bg': goldTheme.colors.glass.background,
  '--theme-glass-border': goldTheme.colors.glass.border,
  '--theme-glass-shadow': goldTheme.colors.glass.shadow,
  '--theme-glass-blur': goldTheme.colors.glass.backdropBlur,
  
  // Backgrounds
  '--theme-bg-primary': goldTheme.backgrounds.primary,
  '--theme-bg-secondary': goldTheme.backgrounds.secondary,
  '--theme-bg-tertiary': goldTheme.backgrounds.tertiary || goldTheme.backgrounds.secondary,
  
  // Text colors
  '--theme-text-primary': goldTheme.colors.text.primary,
  '--theme-text-secondary': goldTheme.colors.text.secondary,
  '--theme-text-accent': goldTheme.colors.text.accent,
  '--theme-text-muted': goldTheme.colors.text.muted,
  
  // Gradients
  '--theme-gradient-primary': goldTheme.gradients.primary,
  '--theme-gradient-secondary': goldTheme.gradients.secondary,
  '--theme-gradient-accent': goldTheme.gradients.accent,
  '--theme-gradient-text': goldTheme.gradients.text,
  '--theme-gradient-button': goldTheme.gradients.button,
  '--theme-gradient-card': goldTheme.gradients.card,
  
  // Shadows
  '--theme-shadow-sm': goldTheme.shadows.sm,
  '--theme-shadow-md': goldTheme.shadows.md,
  '--theme-shadow-lg': goldTheme.shadows.lg,
  '--theme-shadow-xl': goldTheme.shadows.xl,
  '--theme-shadow-glass': goldTheme.shadows.glass,
  '--theme-shadow-premium': goldTheme.shadows.premium,
};
