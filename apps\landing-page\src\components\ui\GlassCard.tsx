import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface GlassCardProps {
  children: ReactNode;
  className?: string;
  variant?: 'default' | 'premium' | 'subtle';
  hover?: boolean;
  onClick?: () => void;
}

export default function GlassCard({ 
  children, 
  className = '', 
  variant = 'default',
  hover = true,
  onClick 
}: GlassCardProps) {
  const baseClasses = 'relative overflow-hidden transition-all duration-300';
  
  const variantClasses = {
    default: 'glass-card',
    premium: 'card-premium',
    subtle: 'bg-white/5 backdrop-blur-md border border-white/10 rounded-xl'
  };

  const hoverProps = hover ? {
    whileHover: { 
      scale: 1.02, 
      y: -2,
      transition: { duration: 0.2 }
    },
    whileTap: { scale: 0.98 }
  } : {};

  return (
    <motion.div
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      onClick={onClick}
      {...hoverProps}
    >
      {children}
    </motion.div>
  );
}
