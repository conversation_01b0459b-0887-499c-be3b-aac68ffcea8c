import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Filter out known extension errors that we can't control
    const isExtensionError = 
      error.message?.includes('Frame with ID') ||
      error.message?.includes('Could not establish connection') ||
      error.message?.includes('MetaMask') ||
      error.message?.includes('chrome-extension') ||
      error.message?.includes('contentscript') ||
      error.message?.includes('serviceWorker');

    if (!isExtensionError) {
      // Only log non-extension errors
      // eslint-disable-next-line no-console
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      // Check if it's an extension error
      const isExtensionError = 
        this.state.error?.message?.includes('Frame with ID') ||
        this.state.error?.message?.includes('Could not establish connection') ||
        this.state.error?.message?.includes('MetaMask') ||
        this.state.error?.message?.includes('chrome-extension') ||
        this.state.error?.message?.includes('contentscript') ||
        this.state.error?.message?.includes('serviceWorker');

      if (isExtensionError) {
        // For extension errors, just render children normally
        return this.props.children;
      }

      // For actual app errors, show fallback UI
      return this.props.fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Something went wrong
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              We&apos;re sorry, but something unexpected happened. Please refresh the page.
            </p>
            <button
              type="button"
              onClick={() => window.location.reload()}
              className="btn-primary"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
