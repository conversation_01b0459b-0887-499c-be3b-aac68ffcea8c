import { ThemeName, ThemeConfig, CSSCustomProperties } from './types';
import { goldTheme, goldThemeCSSProperties } from './gold-theme';
import { purpleTheme, purpleThemeCSSProperties } from './purple-theme';

// Theme registry
export const themes: Record<ThemeName, ThemeConfig> = {
  gold: goldTheme,
  purple: purpleTheme,
};

// CSS Properties registry
export const themeCSSProperties: Record<ThemeName, Partial<CSSCustomProperties>> = {
  gold: goldThemeCSSProperties,
  purple: purpleThemeCSSProperties,
};

// Get theme configuration by name
export const getTheme = (themeName: ThemeName): ThemeConfig => {
  return themes[themeName];
};

// Get CSS properties for theme
export const getThemeCSSProperties = (themeName: ThemeName): Partial<CSSCustomProperties> => {
  return themeCSSProperties[themeName];
};

// Apply theme CSS properties to document root
export const applyThemeToDocument = (themeName: ThemeName): void => {
  const properties = getThemeCSSProperties(themeName);
  const root = document.documentElement;
  
  // Remove existing theme classes
  root.classList.remove('theme-gold', 'theme-purple');
  
  // Add new theme class
  root.classList.add(`theme-${themeName}`);
  
  // Apply CSS custom properties
  Object.entries(properties).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });
};

// Get theme from localStorage
export const getStoredTheme = (): ThemeName | null => {
  if (typeof window === 'undefined') return null;
  
  try {
    const stored = localStorage.getItem('freela-theme');
    return stored && (stored === 'gold' || stored === 'purple') ? stored : null;
  } catch {
    return null;
  }
};

// Store theme in localStorage
export const storeTheme = (themeName: ThemeName): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('freela-theme', themeName);
  } catch {
    // Silently fail if localStorage is not available
  }
};

// Generate theme-aware CSS classes
export const getThemeClasses = (themeName: ThemeName): string => {
  return `theme-${themeName}`;
};

// Check if theme is valid
export const isValidTheme = (theme: string): theme is ThemeName => {
  return theme === 'gold' || theme === 'purple';
};

// Get opposite theme
export const getOppositeTheme = (themeName: ThemeName): ThemeName => {
  return themeName === 'gold' ? 'purple' : 'gold';
};

// Theme transition utilities
export const enableThemeTransitions = (): void => {
  if (typeof document === 'undefined') return;
  
  const style = document.createElement('style');
  style.textContent = `
    * {
      transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease !important;
    }
  `;
  document.head.appendChild(style);
  
  // Remove after transition completes
  setTimeout(() => {
    document.head.removeChild(style);
  }, 300);
};

// Disable theme transitions temporarily
export const disableThemeTransitions = (): void => {
  if (typeof document === 'undefined') return;
  
  const style = document.createElement('style');
  style.textContent = `
    * {
      transition: none !important;
    }
  `;
  document.head.appendChild(style);
  
  // Force reflow
  document.body.offsetHeight;
  
  // Remove immediately
  document.head.removeChild(style);
};

// Generate dynamic CSS for theme
export const generateThemeCSS = (themeName: ThemeName): string => {
  const theme = getTheme(themeName);
  const properties = getThemeCSSProperties(themeName);
  
  return `
    .theme-${themeName} {
      ${Object.entries(properties)
        .map(([property, value]) => `${property}: ${value};`)
        .join('\n      ')}
    }
    
    /* Theme-specific animations */
    ${theme.animations.shimmer}
    ${theme.animations.glow}
    ${theme.animations.pulse}
    ${theme.animations.float}
    
    /* Theme-specific utilities */
    .theme-${themeName} .bg-theme-primary {
      background: var(--theme-bg-primary);
    }
    
    .theme-${themeName} .bg-theme-secondary {
      background: var(--theme-bg-secondary);
    }
    
    .theme-${themeName} .glass-effect {
      background: var(--theme-glass-bg);
      border: 1px solid var(--theme-glass-border);
      box-shadow: var(--theme-glass-shadow);
      backdrop-filter: var(--theme-glass-blur);
      -webkit-backdrop-filter: var(--theme-glass-blur);
    }
    
    .theme-${themeName} .text-theme-gradient {
      background: var(--theme-gradient-text);
      background-size: 300% 100%;
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: ${themeName}Shimmer 4s ease-in-out infinite;
    }
    
    .theme-${themeName} .btn-theme-primary {
      background: var(--theme-gradient-button);
      box-shadow: var(--theme-shadow-premium);
      color: white;
      border: 1px solid var(--theme-glass-border);
    }
    
    .theme-${themeName} .btn-theme-primary:hover {
      animation: ${themeName}Glow 2s ease-in-out infinite;
    }
  `;
};

// Inject theme CSS into document
export const injectThemeCSS = (themeName: ThemeName): void => {
  if (typeof document === 'undefined') return;
  
  // Remove existing theme styles
  const existingStyle = document.getElementById(`theme-${themeName}-styles`);
  if (existingStyle) {
    existingStyle.remove();
  }
  
  // Create new style element
  const style = document.createElement('style');
  style.id = `theme-${themeName}-styles`;
  style.textContent = generateThemeCSS(themeName);
  
  // Append to head
  document.head.appendChild(style);
};

// Initialize theme system
export const initializeTheme = (defaultTheme: ThemeName = 'gold'): ThemeName => {
  const storedTheme = getStoredTheme();
  const initialTheme = storedTheme || defaultTheme;
  
  // Apply theme to document
  applyThemeToDocument(initialTheme);
  
  // Inject theme CSS
  injectThemeCSS(initialTheme);
  
  return initialTheme;
};
