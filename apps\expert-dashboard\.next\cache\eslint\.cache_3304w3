[{"C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\analytics\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\bookings\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\earnings\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\layout.tsx": "4", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\messages\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\profile\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\services\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\error.tsx": "9", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\not-found.tsx": "11", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\providers.tsx": "13", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\dashboard\\BookingStats.tsx": "14", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\dashboard\\EarningsOverview.tsx": "15", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\dashboard\\QuickActions.tsx": "16", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\dashboard\\RecentBookings.tsx": "17", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\dashboard\\ServicePerformance.tsx": "18", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\layout\\Header.tsx": "19", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\layout\\Sidebar.tsx": "20", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\ui\\EmptyState.tsx": "21", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\ui\\LoadingSpinner.tsx": "22", "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\ui\\Modal.tsx": "23"}, {"size": 16267, "mtime": 1749575165906, "results": "24", "hashOfConfig": "25"}, {"size": 14466, "mtime": 1749574989012, "results": "26", "hashOfConfig": "25"}, {"size": 15972, "mtime": 1749575048918, "results": "27", "hashOfConfig": "25"}, {"size": 814, "mtime": 1749556331157, "results": "28", "hashOfConfig": "25"}, {"size": 14830, "mtime": 1749575106143, "results": "29", "hashOfConfig": "25"}, {"size": 1171, "mtime": 1749555986676, "results": "30", "hashOfConfig": "25"}, {"size": 15700, "mtime": 1749565122580, "results": "31", "hashOfConfig": "25"}, {"size": 13695, "mtime": 1749565182526, "results": "32", "hashOfConfig": "25"}, {"size": 1491, "mtime": 1749559232452, "results": "33", "hashOfConfig": "25"}, {"size": 1060, "mtime": 1749559208582, "results": "34", "hashOfConfig": "25"}, {"size": 1093, "mtime": 1749559222569, "results": "35", "hashOfConfig": "25"}, {"size": 137, "mtime": 1749555975930, "results": "36", "hashOfConfig": "25"}, {"size": 1372, "mtime": 1749555948746, "results": "37", "hashOfConfig": "25"}, {"size": 3409, "mtime": 1749556439770, "results": "38", "hashOfConfig": "25"}, {"size": 2580, "mtime": 1749556857040, "results": "39", "hashOfConfig": "25"}, {"size": 2326, "mtime": 1749556403144, "results": "40", "hashOfConfig": "25"}, {"size": 4178, "mtime": 1749556909644, "results": "41", "hashOfConfig": "25"}, {"size": 4528, "mtime": 1749556463488, "results": "42", "hashOfConfig": "25"}, {"size": 4776, "mtime": 1749556376925, "results": "43", "hashOfConfig": "25"}, {"size": 5223, "mtime": 1749556353627, "results": "44", "hashOfConfig": "25"}, {"size": 1456, "mtime": 1749574938465, "results": "45", "hashOfConfig": "25"}, {"size": 1531, "mtime": 1749574928129, "results": "46", "hashOfConfig": "25"}, {"size": 3091, "mtime": 1749574915670, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "8923nx", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\analytics\\page.tsx", ["117", "118"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\bookings\\page.tsx", ["119", "120", "121"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\earnings\\page.tsx", ["122", "123"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\messages\\page.tsx", ["124", "125", "126", "127"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\dashboard\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\error.tsx", ["128"], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\app\\providers.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\dashboard\\BookingStats.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\dashboard\\EarningsOverview.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\dashboard\\QuickActions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\dashboard\\RecentBookings.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\dashboard\\ServicePerformance.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\ui\\EmptyState.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Freela\\apps\\expert-dashboard\\src\\components\\ui\\Modal.tsx", [], [], {"ruleId": "129", "severity": 2, "message": "130", "line": 5, "column": 3, "nodeType": "131", "messageId": "132", "endLine": 5, "endColumn": 15}, {"ruleId": "129", "severity": 2, "message": "133", "line": 7, "column": 3, "nodeType": "131", "messageId": "132", "endLine": 7, "endColumn": 16}, {"ruleId": "134", "severity": 1, "message": "135", "line": 111, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 111, "endColumn": 20, "suggestions": "138"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 126, "column": 9, "nodeType": "136", "messageId": "137", "endLine": 126, "endColumn": 22, "suggestions": "139"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 141, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 141, "endColumn": 20, "suggestions": "140"}, {"ruleId": "129", "severity": 2, "message": "141", "line": 12, "column": 10, "nodeType": "131", "messageId": "132", "endLine": 12, "endColumn": 22}, {"ruleId": "134", "severity": 1, "message": "135", "line": 140, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 140, "endColumn": 20, "suggestions": "142"}, {"ruleId": "129", "severity": 2, "message": "143", "line": 8, "column": 3, "nodeType": "131", "messageId": "132", "endLine": 8, "endColumn": 11}, {"ruleId": "129", "severity": 2, "message": "144", "line": 9, "column": 3, "nodeType": "131", "messageId": "132", "endLine": 9, "endColumn": 12}, {"ruleId": "129", "severity": 2, "message": "141", "line": 11, "column": 10, "nodeType": "131", "messageId": "132", "endLine": 11, "endColumn": 22}, {"ruleId": "134", "severity": 1, "message": "135", "line": 185, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 185, "endColumn": 20, "suggestions": "145"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 13, "column": 5, "nodeType": "136", "messageId": "137", "endLine": 13, "endColumn": 18, "suggestions": "146"}, "@typescript-eslint/no-unused-vars", "'ChartBarIcon' is defined but never used.", "Identifier", "unusedVar", "'UserGroupIcon' is defined but never used.", "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["147"], ["148"], ["149"], "'LoadingState' is defined but never used.", ["150"], "'UserIcon' is defined but never used.", "'ClockIcon' is defined but never used.", ["151"], ["152"], {"messageId": "153", "data": "154", "fix": "155", "desc": "156"}, {"messageId": "153", "data": "157", "fix": "158", "desc": "156"}, {"messageId": "153", "data": "159", "fix": "160", "desc": "156"}, {"messageId": "153", "data": "161", "fix": "162", "desc": "156"}, {"messageId": "153", "data": "163", "fix": "164", "desc": "156"}, {"messageId": "153", "data": "165", "fix": "166", "desc": "156"}, "removeConsole", {"propertyName": "167"}, {"range": "168", "text": "169"}, "Remove the console.error().", {"propertyName": "167"}, {"range": "170", "text": "169"}, {"propertyName": "167"}, {"range": "171", "text": "169"}, {"propertyName": "167"}, {"range": "172", "text": "169"}, {"propertyName": "167"}, {"range": "173", "text": "169"}, {"propertyName": "167"}, {"range": "174", "text": "169"}, "error", [3044, 3093], "", [3558, 3607], [4010, 4060], [3770, 3823], [5005, 5052], [194, 215]]