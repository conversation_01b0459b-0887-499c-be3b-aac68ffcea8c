import type { AppProps } from 'next/app';
import { appWithTranslation } from 'next-i18next';
import { ThemeProvider as NextThemeProvider } from 'next-themes';
import { Toaster } from 'react-hot-toast';
import { Inter, Noto_Sans_Arabic, Poppins, Cairo, Tajawal } from 'next/font/google';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import ErrorBoundary from '@/components/ErrorBoundary';
import { initializeErrorFilter } from '@/utils/errorFilter';
import { ThemeProvider } from '@/themes';
import ThemeController from '@/components/ThemeController';

import '@/styles/globals.css';

// Font configurations
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const notoSansArabic = Noto_Sans_Arabic({
  subsets: ['arabic'],
  variable: '--font-arabic',
  display: 'swap',
});

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-display',
  display: 'swap',
});

// Enhanced Arabic fonts for better typography
const cairo = Cairo({
  subsets: ['arabic', 'latin'],
  weight: ['300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-cairo',
  display: 'swap',
});

const tajawal = Tajawal({
  subsets: ['arabic', 'latin'],
  weight: ['300', '400', '500', '700', '800', '900'],
  variable: '--font-tajawal',
  display: 'swap',
});

function App({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';

  useEffect(() => {
    // Set document direction based on locale
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = locale || 'ar';

    // Initialize error filtering for extension errors
    initializeErrorFilter();
  }, [locale, isRTL]);

  return (
    <ErrorBoundary>
      <div
        className={`${inter.variable} ${notoSansArabic.variable} ${poppins.variable} ${cairo.variable} ${tajawal.variable} ${
          isRTL ? 'font-cairo' : 'font-sans'
        }`}
      >
        <ThemeProvider defaultTheme="gold">
          <NextThemeProvider
            attribute="class"
            defaultTheme="dark"
            enableSystem={false}
            disableTransitionOnChange={false}
          >
            <Component {...pageProps} />
            <ThemeController />
            <Toaster
              position={isRTL ? 'top-left' : 'top-right'}
              toastOptions={{
                duration: 4000,
                style: {
                  background: 'var(--toast-bg)',
                  color: 'var(--toast-color)',
                  direction: isRTL ? 'rtl' : 'ltr',
                },
              }}
            />
          </NextThemeProvider>
        </ThemeProvider>
      </div>
    </ErrorBoundary>
  );
}

export default appWithTranslation(App);
