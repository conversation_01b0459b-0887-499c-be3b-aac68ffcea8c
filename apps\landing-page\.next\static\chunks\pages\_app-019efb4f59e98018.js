(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[888],{3463:function(e,t,r){"use strict";var i=r(8570),n={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},s={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},o={};function l(e){return i.isMemo(e)?a:o[e.$$typeof]||n}o[i.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},o[i.Memo]=a;var u=Object.defineProperty,c=Object.getOwnPropertyNames,h=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,f=Object.prototype;e.exports=function e(t,r,i){if("string"!=typeof r){if(f){var n=p(r);n&&n!==f&&e(t,n,i)}var a=c(r);h&&(a=a.concat(h(r)));for(var o=l(t),m=l(r),g=0;g<a.length;++g){var y=a[g];if(!s[y]&&!(i&&i[y])&&!(m&&m[y])&&!(o&&o[y])){var v=d(r,y);try{u(t,y,v)}catch(e){}}}}return t}},34:function(e,t,r){"use strict";let i;r.d(t,{Jc:function(){return eB},$G:function(){return y}});var n=r(2784);r(4896),Object.create(null);let s={};function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];"string"==typeof t[0]&&s[t[0]]||("string"==typeof t[0]&&(s[t[0]]=new Date),function(){if(console&&console.warn){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];"string"==typeof t[0]&&(t[0]=`react-i18next:: ${t[0]}`),console.warn(...t)}}(...t))}let o=(e,t)=>()=>{if(e.isInitialized)t();else{let r=()=>{setTimeout(()=>{e.off("initialized",r)},0),t()};e.on("initialized",r)}};function l(e,t,r){e.loadNamespaces(t,o(e,r))}function u(e,t,r,i){"string"==typeof r&&(r=[r]),r.forEach(t=>{0>e.options.ns.indexOf(t)&&e.options.ns.push(t)}),e.loadLanguages(t,o(e,i))}let c=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,h={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},d=e=>h[e],p={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(c,d)},f=(0,n.createContext)();class m{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}let g=(e,t)=>{let r=(0,n.useRef)();return(0,n.useEffect)(()=>{r.current=t?r.current:e},[e,t]),r.current};function y(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{i18n:r}=t,{i18n:s,defaultNS:o}=(0,n.useContext)(f)||{},c=r||s||i;if(c&&!c.reportNamespaces&&(c.reportNamespaces=new m),!c){a("You will need to pass in an i18next instance by using initReactI18next");let e=(e,t)=>"string"==typeof t?t:t&&"object"==typeof t&&"string"==typeof t.defaultValue?t.defaultValue:Array.isArray(e)?e[e.length-1]:e,t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}c.options.react&&void 0!==c.options.react.wait&&a("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");let h={...p,...c.options.react,...t},{useSuspense:d,keyPrefix:y}=h,v=e||o||c.options&&c.options.defaultNS;v="string"==typeof v?[v]:v||["translation"],c.reportNamespaces.addUsedNamespaces&&c.reportNamespaces.addUsedNamespaces(v);let b=(c.isInitialized||c.initializedStoreOnce)&&v.every(e=>(function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!t.languages||!t.languages.length)return a("i18n.languages were undefined or empty",t.languages),!0;let i=void 0!==t.options.ignoreJSONStructure;return i?t.hasLoadedNamespace(e,{lng:r.lng,precheck:(t,i)=>{if(r.bindI18n&&r.bindI18n.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!i(t.isLanguageChangingTo,e))return!1}}):function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=t.languages[0],n=!!t.options&&t.options.fallbackLng,s=t.languages[t.languages.length-1];if("cimode"===i.toLowerCase())return!0;let a=(e,r)=>{let i=t.services.backendConnector.state[`${e}|${r}`];return -1===i||2===i};return(!(r.bindI18n&&r.bindI18n.indexOf("languageChanging")>-1)||!t.services.backendConnector.backend||!t.isLanguageChangingTo||!!a(t.isLanguageChangingTo,e))&&!!(t.hasResourceBundle(i,e)||!t.services.backendConnector.backend||t.options.resources&&!t.options.partialBundledLanguages||a(i,e)&&(!n||a(s,e)))}(e,t,r)})(e,c,h));function x(){return c.getFixedT(t.lng||null,"fallback"===h.nsMode?v:v[0],y)}let[w,S]=(0,n.useState)(x),k=v.join();t.lng&&(k=`${t.lng}${k}`);let P=g(k),E=(0,n.useRef)(!0);(0,n.useEffect)(()=>{let{bindI18n:e,bindI18nStore:r}=h;function i(){E.current&&S(x)}return E.current=!0,b||d||(t.lng?u(c,t.lng,v,()=>{E.current&&S(x)}):l(c,v,()=>{E.current&&S(x)})),b&&P&&P!==k&&E.current&&S(x),e&&c&&c.on(e,i),r&&c&&c.store.on(r,i),()=>{E.current=!1,e&&c&&e.split(" ").forEach(e=>c.off(e,i)),r&&c&&r.split(" ").forEach(e=>c.store.off(e,i))}},[c,k]);let C=(0,n.useRef)(!0);(0,n.useEffect)(()=>{E.current&&!C.current&&S(x),C.current=!1},[c,y]);let L=[w,c,b];if(L.t=w,L.i18n=c,L.ready=b,b||!b&&!d)return L;throw new Promise(e=>{t.lng?u(c,t.lng,v,()=>e()):l(c,v,()=>e())})}function v(e){let{i18n:t,defaultNS:r,children:i}=e,s=(0,n.useMemo)(()=>({i18n:t,defaultNS:r}),[t,r]);return(0,n.createElement)(f.Provider,{value:s},i)}function b(){return(b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var i in r)({}).hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e}).apply(null,arguments)}function x(e){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function w(e,t,r){var i;return(i=function(e,t){if("object"!=x(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=x(i))return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==x(i)?i:i+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var S=r(3463),k=r.n(S);function P(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=Array(t);r<t;r++)i[r]=e[r];return i}function E(e,t){if(e){if("string"==typeof e)return P(e,t);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?P(e,t):void 0}}function C(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var i,n,s,a,o=[],l=!0,u=!1;try{if(s=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(i=s.call(r)).done)&&(o.push(i.value),o.length!==t);l=!0);}catch(e){u=!0,n=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw n}}return o}}(e,t)||E(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(e,t){if(null==e)return{};var r,i,n=function(e,t){if(null==e)return{};var r={};for(var i in e)if(({}).hasOwnProperty.call(e,i)){if(-1!==t.indexOf(i))continue;r[i]=e[i]}return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(i=0;i<s.length;i++)r=s[i],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}var j={defaultNS:"common",errorStackTraceLimit:0,i18n:{defaultLocale:"en",locales:["en"]},get initImmediate(){return"undefined"!=typeof window},get initAsync(){return"undefined"!=typeof window},interpolation:{escapeValue:!1},load:"currentOnly",localeExtension:"json",localePath:"./public/locales",localeStructure:"{{lng}}/{{ns}}",react:{useSuspense:!1},reloadOnPrerender:!1,serializeConfig:!0,use:[]},T="undefined"!=typeof window?n.useLayoutEffect:n.useEffect,A=["i18n"],O=["i18n"];function R(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function N(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?R(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var V=["backend","detection"],D=function(e){if("string"!=typeof(null==e?void 0:e.lng))throw Error("config.lng was not passed into createConfig");var t,r,i,n=e.i18n,s=L(e,A),a=j.i18n,o=N(N(N(N({},L(j,O)),s),a),n),l=o.defaultNS,u=o.lng,c=o.localeExtension,h=o.localePath,d=o.nonExplicitSupportedLngs,p=o.locales.filter(function(e){return"default"!==e});if("cimode"===u)return o;if(void 0===o.fallbackLng&&(o.fallbackLng=o.defaultLocale,"default"===o.fallbackLng)){var f=C(p,1);o.fallbackLng=f[0]}var m=null==e||null===(t=e.interpolation)||void 0===t?void 0:t.prefix,g=null==e||null===(r=e.interpolation)||void 0===r?void 0:r.suffix,y=null!=m?m:"{{",v=null!=g?g:"}}";"string"!=typeof(null==e?void 0:e.localeStructure)&&(m||g)&&(o.localeStructure="".concat(y,"lng").concat(v,"/").concat(y,"ns").concat(v));var b=o.fallbackLng,w=o.localeStructure;if(d){var S=function(e,t){var r=C(t.split("-"),1)[0];return e[t]=[r],e};if("string"==typeof b)o.fallbackLng=o.locales.filter(function(e){return e.includes("-")}).reduce(S,{default:[b]});else if(Array.isArray(b))o.fallbackLng=o.locales.filter(function(e){return e.includes("-")}).reduce(S,{default:b});else if("object"===x(b))o.fallbackLng=Object.entries(o.fallbackLng).reduce(function(e,t){var r,i=C(t,2),n=i[0],s=i[1];return e[n]=n.includes("-")?(r=[n.split("-")[0]].concat(function(e){if(Array.isArray(e))return P(e)}(s)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(s)||E(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),Array.from(new Set(r))):s,e},b);else if("function"==typeof b)throw Error("If nonExplicitSupportedLngs is true, no functions are allowed for fallbackLng")}return(null==e||null===(i=e.use)||void 0===i?void 0:i.some(function(e){return"backend"===e.type}))||("string"==typeof h?o.backend={addPath:"".concat(h,"/").concat(w,".missing.").concat(c),loadPath:"".concat(h,"/").concat(w,".").concat(c)}:"function"!=typeof h||(o.backend={addPath:function(e,t){return h(e,t,!0)},loadPath:function(e,t){return h(e,t,!1)}})),"string"==typeof o.ns||Array.isArray(o.ns)||(o.ns=[l]),V.forEach(function(t){e[t]&&(o[t]=N(N({},o[t]),e[t]))}),o};let M=e=>"string"==typeof e,$=()=>{let e,t;let r=new Promise((r,i)=>{e=r,t=i});return r.resolve=e,r.reject=t,r},F=e=>null==e?"":""+e,I=(e,t,r)=>{e.forEach(e=>{t[e]&&(r[e]=t[e])})},B=/###/g,U=e=>e&&e.indexOf("###")>-1?e.replace(B,"."):e,_=e=>!e||M(e),W=(e,t,r)=>{let i=M(t)?t.split("."):t,n=0;for(;n<i.length-1;){if(_(e))return{};let t=U(i[n]);!e[t]&&r&&(e[t]=new r),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++n}return _(e)?{}:{obj:e,k:U(i[n])}},z=(e,t,r)=>{let{obj:i,k:n}=W(e,t,Object);if(void 0!==i||1===t.length){i[n]=r;return}let s=t[t.length-1],a=t.slice(0,t.length-1),o=W(e,a,Object);for(;void 0===o.obj&&a.length;)s=`${a[a.length-1]}.${s}`,(o=W(e,a=a.slice(0,a.length-1),Object))&&o.obj&&void 0!==o.obj[`${o.k}.${s}`]&&(o.obj=void 0);o.obj[`${o.k}.${s}`]=r},H=(e,t,r,i)=>{let{obj:n,k:s}=W(e,t,Object);n[s]=n[s]||[],n[s].push(r)},K=(e,t)=>{let{obj:r,k:i}=W(e,t);if(r)return r[i]},Z=(e,t,r)=>{let i=K(e,r);return void 0!==i?i:K(t,r)},Y=(e,t,r)=>{for(let i in t)"__proto__"!==i&&"constructor"!==i&&(i in e?M(e[i])||e[i]instanceof String||M(t[i])||t[i]instanceof String?r&&(e[i]=t[i]):Y(e[i],t[i],r):e[i]=t[i]);return e},q=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var G={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let J=e=>M(e)?e.replace(/[&<>"'\/]/g,e=>G[e]):e;class X{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){let t=this.regExpMap.get(e);if(void 0!==t)return t;let r=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,r),this.regExpQueue.push(e),r}}let Q=[" ",",","?","!",";"],ee=new X(20),et=(e,t,r)=>{t=t||"",r=r||"";let i=Q.filter(e=>0>t.indexOf(e)&&0>r.indexOf(e));if(0===i.length)return!0;let n=ee.getRegExp(`(${i.map(e=>"?"===e?"\\?":e).join("|")})`),s=!n.test(e);if(!s){let t=e.indexOf(r);t>0&&!n.test(e.substring(0,t))&&(s=!0)}return s},er=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t])return e[t];let i=t.split(r),n=e;for(let e=0;e<i.length;){let t;if(!n||"object"!=typeof n)return;let s="";for(let a=e;a<i.length;++a)if(a!==e&&(s+=r),s+=i[a],void 0!==(t=n[s])){if(["string","number","boolean"].indexOf(typeof t)>-1&&a<i.length-1)continue;e+=a-e+1;break}n=t}return n},ei=e=>e&&e.replace("_","-"),en={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class es{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||en,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,r,i){return i&&!this.debug?null:(M(e[0])&&(e[0]=`${r}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new es(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new es(this.logger,e)}}var ea=new es;class eo{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);let r=this.observers[e].get(t)||0;this.observers[e].set(t,r+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];if(this.observers[e]){let t=Array.from(this.observers[e].entries());t.forEach(e=>{let[t,i]=e;for(let e=0;e<i;e++)t(...r)})}if(this.observers["*"]){let t=Array.from(this.observers["*"].entries());t.forEach(t=>{let[i,n]=t;for(let t=0;t<n;t++)i.apply(i,[e,...r])})}}}class el extends eo{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}removeNamespaces(e){let t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,r){let i,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=void 0!==n.keySeparator?n.keySeparator:this.options.keySeparator,a=void 0!==n.ignoreJSONStructure?n.ignoreJSONStructure:this.options.ignoreJSONStructure;e.indexOf(".")>-1?i=e.split("."):(i=[e,t],r&&(Array.isArray(r)?i.push(...r):M(r)&&s?i.push(...r.split(s)):i.push(r)));let o=K(this.data,i);return(!o&&!t&&!r&&e.indexOf(".")>-1&&(e=i[0],t=i[1],r=i.slice(2).join(".")),!o&&a&&M(r))?er(this.data&&this.data[e]&&this.data[e][t],r,s):o}addResource(e,t,r,i){let n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1},s=void 0!==n.keySeparator?n.keySeparator:this.options.keySeparator,a=[e,t];r&&(a=a.concat(s?r.split(s):r)),e.indexOf(".")>-1&&(a=e.split("."),i=t,t=a[1]),this.addNamespaces(t),z(this.data,a,i),n.silent||this.emit("added",e,t,r,i)}addResources(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(let i in r)(M(r[i])||Array.isArray(r[i]))&&this.addResource(e,t,i,r[i],{silent:!0});i.silent||this.emit("added",e,t,r)}addResourceBundle(e,t,r,i,n){let s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),i=r,r=t,t=a[1]),this.addNamespaces(t);let o=K(this.data,a)||{};s.skipCopy||(r=JSON.parse(JSON.stringify(r))),i?Y(o,r,n):o={...o,...r},z(this.data,a,o),s.silent||this.emit("added",e,t,r)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return(t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI)?{...this.getResource(e,t)}:this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){let t=this.getDataByLanguage(e),r=t&&Object.keys(t)||[];return!!r.find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}}var eu={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,r,i,n){return e.forEach(e=>{this.processors[e]&&(t=this.processors[e].process(t,r,i,n))}),t}};let ec={};class eh extends eo{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),I(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=ea.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;let r=this.resolve(e,t);return r&&void 0!==r.res}extractFromKey(e,t){let r=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===r&&(r=":");let i=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,n=t.ns||this.options.defaultNS||[],s=r&&e.indexOf(r)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!et(e,r,i);if(s&&!a){let t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:M(n)?[n]:n};let s=e.split(r);(r!==i||r===i&&this.options.ns.indexOf(s[0])>-1)&&(n=s.shift()),e=s.join(i)}return{key:e,namespaces:M(n)?[n]:n}}translate(e,t,r){if("object"!=typeof t&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof t&&(t={...t}),t||(t={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);let i=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,n=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,{key:s,namespaces:a}=this.extractFromKey(e[e.length-1],t),o=a[a.length-1],l=t.lng||this.language,u=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(l&&"cimode"===l.toLowerCase()){if(u){let e=t.nsSeparator||this.options.nsSeparator;return i?{res:`${o}${e}${s}`,usedKey:s,exactUsedKey:s,usedLng:l,usedNS:o,usedParams:this.getUsedParamsDetails(t)}:`${o}${e}${s}`}return i?{res:s,usedKey:s,exactUsedKey:s,usedLng:l,usedNS:o,usedParams:this.getUsedParamsDetails(t)}:s}let c=this.resolve(e,t),h=c&&c.res,d=c&&c.usedKey||s,p=c&&c.exactUsedKey||s,f=Object.prototype.toString.apply(h),m=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,g=!this.i18nFormat||this.i18nFormat.handleAsObject,y=!M(h)&&"boolean"!=typeof h&&"number"!=typeof h;if(g&&h&&y&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf(f)&&!(M(m)&&Array.isArray(h))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(d,h,{...t,ns:a}):`key '${s} (${this.language})' returned an object instead of string.`;return i?(c.res=e,c.usedParams=this.getUsedParamsDetails(t),c):e}if(n){let e=Array.isArray(h),r=e?[]:{},i=e?p:d;for(let e in h)if(Object.prototype.hasOwnProperty.call(h,e)){let s=`${i}${n}${e}`;r[e]=this.translate(s,{...t,joinArrays:!1,ns:a}),r[e]===s&&(r[e]=h[e])}h=r}}else if(g&&M(m)&&Array.isArray(h))(h=h.join(m))&&(h=this.extendTranslation(h,e,t,r));else{let i=!1,a=!1,u=void 0!==t.count&&!M(t.count),d=eh.hasDefaultValue(t),p=u?this.pluralResolver.getSuffix(l,t.count,t):"",f=t.ordinal&&u?this.pluralResolver.getSuffix(l,t.count,{ordinal:!1}):"",m=u&&!t.ordinal&&0===t.count&&this.pluralResolver.shouldUseIntlApi(),g=m&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${p}`]||t[`defaultValue${f}`]||t.defaultValue;!this.isValidLookup(h)&&d&&(i=!0,h=g),this.isValidLookup(h)||(a=!0,h=s);let y=t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey,v=y&&a?void 0:h,b=d&&g!==h&&this.options.updateMissing;if(a||i||b){if(this.logger.log(b?"updateKey":"missingKey",l,o,s,b?g:h),n){let e=this.resolve(s,{...t,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[],r=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&r&&r[0])for(let t=0;t<r.length;t++)e.push(r[t]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(t.lng||this.language):e.push(t.lng||this.language);let i=(e,r,i)=>{let n=d&&i!==h?i:v;this.options.missingKeyHandler?this.options.missingKeyHandler(e,o,r,n,b,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,o,r,n,b,t),this.emit("missingKey",e,o,r,h)};this.options.saveMissing&&(this.options.saveMissingPlurals&&u?e.forEach(e=>{let r=this.pluralResolver.getSuffixes(e,t);m&&t[`defaultValue${this.options.pluralSeparator}zero`]&&0>r.indexOf(`${this.options.pluralSeparator}zero`)&&r.push(`${this.options.pluralSeparator}zero`),r.forEach(r=>{i([e],s+r,t[`defaultValue${r}`]||g)})}):i(e,s,g))}h=this.extendTranslation(h,e,t,c,r),a&&h===s&&this.options.appendNamespaceToMissingKey&&(h=`${o}:${s}`),(a||i)&&this.options.parseMissingKeyHandler&&(h="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${o}:${s}`:s,i?h:void 0):this.options.parseMissingKeyHandler(h))}return i?(c.res=h,c.usedParams=this.getUsedParamsDetails(t),c):h}extendTranslation(e,t,r,i,n){var s=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||i.usedLng,i.usedNS,i.usedKey,{resolved:i});else if(!r.skipInterpolation){let a;r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});let o=M(e)&&(r&&r.interpolation&&void 0!==r.interpolation.skipOnVariables?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(o){let t=e.match(this.interpolator.nestingRegexp);a=t&&t.length}let l=r.replace&&!M(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(l={...this.options.interpolation.defaultVariables,...l}),e=this.interpolator.interpolate(e,l,r.lng||this.language||i.usedLng,r),o){let t=e.match(this.interpolator.nestingRegexp),i=t&&t.length;a<i&&(r.nest=!1)}!r.lng&&"v1"!==this.options.compatibilityAPI&&i&&i.res&&(r.lng=this.language||i.usedLng),!1!==r.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,i=Array(e),a=0;a<e;a++)i[a]=arguments[a];return n&&n[0]===i[0]&&!r.context?(s.logger.warn(`It seems you are nesting recursively key: ${i[0]} in key: ${t[0]}`),null):s.translate(...i,t)},r)),r.interpolation&&this.interpolator.reset()}let a=r.postProcess||this.options.postProcess,o=M(a)?[a]:a;return null!=e&&o&&o.length&&!1!==r.applyPostProcessor&&(e=eu.handle(o,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...i,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),e}resolve(e){let t,r,i,n,s,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return M(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(t))return;let o=this.extractFromKey(e,a),l=o.key;r=l;let u=o.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));let c=void 0!==a.count&&!M(a.count),h=c&&!a.ordinal&&0===a.count&&this.pluralResolver.shouldUseIntlApi(),d=void 0!==a.context&&(M(a.context)||"number"==typeof a.context)&&""!==a.context,p=a.lngs?a.lngs:this.languageUtils.toResolveHierarchy(a.lng||this.language,a.fallbackLng);u.forEach(e=>{this.isValidLookup(t)||(s=e,!ec[`${p[0]}-${e}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(s)&&(ec[`${p[0]}-${e}`]=!0,this.logger.warn(`key "${r}" for languages "${p.join(", ")}" won't get resolved as namespace "${s}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach(r=>{let s;if(this.isValidLookup(t))return;n=r;let o=[l];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(o,l,r,e,a);else{let e;c&&(e=this.pluralResolver.getSuffix(r,a.count,a));let t=`${this.options.pluralSeparator}zero`,i=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(c&&(o.push(l+e),a.ordinal&&0===e.indexOf(i)&&o.push(l+e.replace(i,this.options.pluralSeparator)),h&&o.push(l+t)),d){let r=`${l}${this.options.contextSeparator}${a.context}`;o.push(r),c&&(o.push(r+e),a.ordinal&&0===e.indexOf(i)&&o.push(r+e.replace(i,this.options.pluralSeparator)),h&&o.push(r+t))}}for(;s=o.pop();)this.isValidLookup(t)||(i=s,t=this.getResource(r,e,s,a))}))})}),{res:t,usedKey:r,exactUsedKey:i,usedLng:n,usedNS:s}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,r,i):this.resourceStore.getResource(e,t,r,i)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.replace&&!M(e.replace),r=t?e.replace:e;if(t&&void 0!==e.count&&(r.count=e.count),this.options.interpolation.defaultVariables&&(r={...this.options.interpolation.defaultVariables,...r}),!t)for(let e of(r={...r},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete r[e];return r}static hasDefaultValue(e){let t="defaultValue";for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t===r.substring(0,t.length)&&void 0!==e[r])return!0;return!1}}let ed=e=>e.charAt(0).toUpperCase()+e.slice(1);class ep{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=ea.create("languageUtils")}getScriptPartFromCode(e){if(!(e=ei(e))||0>e.indexOf("-"))return null;let t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase())?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(!(e=ei(e))||0>e.indexOf("-"))return e;let t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(M(e)&&e.indexOf("-")>-1){if("undefined"!=typeof Intl&&void 0!==Intl.getCanonicalLocales)try{let t=Intl.getCanonicalLocales(e)[0];if(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)return t}catch(e){}let t=["hans","hant","latn","cyrl","cans","mong","arab"],r=e.split("-");return this.options.lowerCaseLng?r=r.map(e=>e.toLowerCase()):2===r.length?(r[0]=r[0].toLowerCase(),r[1]=r[1].toUpperCase(),t.indexOf(r[1].toLowerCase())>-1&&(r[1]=ed(r[1].toLowerCase()))):3===r.length&&(r[0]=r[0].toLowerCase(),2===r[1].length&&(r[1]=r[1].toUpperCase()),"sgn"!==r[0]&&2===r[2].length&&(r[2]=r[2].toUpperCase()),t.indexOf(r[1].toLowerCase())>-1&&(r[1]=ed(r[1].toLowerCase())),t.indexOf(r[2].toLowerCase())>-1&&(r[2]=ed(r[2].toLowerCase()))),r.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){let t;return e?(e.forEach(e=>{if(t)return;let r=this.formatLanguageCode(e);(!this.options.supportedLngs||this.isSupportedCode(r))&&(t=r)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;let r=this.getLanguagePartFromCode(e);if(this.isSupportedCode(r))return t=r;t=this.options.supportedLngs.find(e=>{if(e===r||!(0>e.indexOf("-")&&0>r.indexOf("-"))&&(e.indexOf("-")>0&&0>r.indexOf("-")&&e.substring(0,e.indexOf("-"))===r||0===e.indexOf(r)&&r.length>1))return e})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),M(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let r=e[t];return r||(r=e[this.getScriptPartFromCode(t)]),r||(r=e[this.formatLanguageCode(t)]),r||(r=e[this.getLanguagePartFromCode(t)]),r||(r=e.default),r||[]}toResolveHierarchy(e,t){let r=this.getFallbackCodes(t||this.options.fallbackLng||[],e),i=[],n=e=>{e&&(this.isSupportedCode(e)?i.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return M(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&n(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&n(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&n(this.getLanguagePartFromCode(e))):M(e)&&n(this.formatLanguageCode(e)),r.forEach(e=>{0>i.indexOf(e)&&n(this.formatLanguageCode(e))}),i}}let ef=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],em={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)},eg=["v1","v2","v3"],ey=["v4"],ev={zero:0,one:1,two:2,few:3,many:4,other:5},eb=()=>{let e={};return ef.forEach(t=>{t.lngs.forEach(r=>{e[r]={numbers:t.nr,plurals:em[t.fc]}})}),e};class ex{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=ea.create("pluralResolver"),(!this.options.compatibilityJSON||ey.includes(this.options.compatibilityJSON))&&("undefined"==typeof Intl||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=eb(),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){let r;let i=ei("dev"===e?"en":e),n=t.ordinal?"ordinal":"cardinal",s=JSON.stringify({cleanedCode:i,type:n});if(s in this.pluralRulesCache)return this.pluralRulesCache[s];try{r=new Intl.PluralRules(i,{type:n})}catch(n){if(!e.match(/-|_/))return;let i=this.languageUtils.getLanguagePartFromCode(e);r=this.getRule(i,t)}return this.pluralRulesCache[s]=r,r}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.getRule(e,t);return this.shouldUseIntlApi()?r&&r.resolvedOptions().pluralCategories.length>1:r&&r.numbers.length>1}getPluralFormsOfKey(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,r).map(e=>`${t}${e}`)}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.getRule(e,t);return r?this.shouldUseIntlApi()?r.resolvedOptions().pluralCategories.sort((e,t)=>ev[e]-ev[t]).map(e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`):r.numbers.map(r=>this.getSuffix(e,r,t)):[]}getSuffix(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=this.getRule(e,r);return i?this.shouldUseIntlApi()?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${i.select(t)}`:this.getSuffixRetroCompatible(i,t):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,t){let r=e.noAbs?e.plurals(t):e.plurals(Math.abs(t)),i=e.numbers[r];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===i?i="plural":1===i&&(i=""));let n=()=>this.options.prepend&&i.toString()?this.options.prepend+i.toString():i.toString();return"v1"===this.options.compatibilityJSON?1===i?"":"number"==typeof i?`_plural_${i.toString()}`:n():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?n():this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString()}shouldUseIntlApi(){return!eg.includes(this.options.compatibilityJSON)}}let ew=function(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",n=!(arguments.length>4)||void 0===arguments[4]||arguments[4],s=Z(e,t,r);return!s&&n&&M(r)&&void 0===(s=er(e,r,i))&&(s=er(t,r,i)),s},eS=e=>e.replace(/\$/g,"$$$$");class ek{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=ea.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});let{escape:t,escapeValue:r,useRawValueToEscape:i,prefix:n,prefixEscaped:s,suffix:a,suffixEscaped:o,formatSeparator:l,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:h,nestingPrefixEscaped:d,nestingSuffix:p,nestingSuffixEscaped:f,nestingOptionsSeparator:m,maxReplaces:g,alwaysFormat:y}=e.interpolation;this.escape=void 0!==t?t:J,this.escapeValue=void 0===r||r,this.useRawValueToEscape=void 0!==i&&i,this.prefix=n?q(n):s||"{{",this.suffix=a?q(a):o||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=h?q(h):d||q("$t("),this.nestingSuffix=p?q(p):f||q(")"),this.nestingOptionsSeparator=m||",",this.maxReplaces=g||1e3,this.alwaysFormat=void 0!==y&&y,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let e=(e,t)=>e&&e.source===t?(e.lastIndex=0,e):RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,r,i){let n,s,a;let o=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},l=e=>{if(0>e.indexOf(this.formatSeparator)){let n=ew(t,o,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(n,void 0,r,{...i,...t,interpolationkey:e}):n}let n=e.split(this.formatSeparator),s=n.shift().trim(),a=n.join(this.formatSeparator).trim();return this.format(ew(t,o,s,this.options.keySeparator,this.options.ignoreJSONStructure),a,r,{...i,...t,interpolationkey:s})};this.resetRegExp();let u=i&&i.missingInterpolationHandler||this.options.missingInterpolationHandler,c=i&&i.interpolation&&void 0!==i.interpolation.skipOnVariables?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables,h=[{regex:this.regexpUnescape,safeValue:e=>eS(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?eS(this.escape(e)):eS(e)}];return h.forEach(t=>{for(a=0;n=t.regex.exec(e);){let r=n[1].trim();if(void 0===(s=l(r))){if("function"==typeof u){let t=u(e,n,i);s=M(t)?t:""}else if(i&&Object.prototype.hasOwnProperty.call(i,r))s="";else if(c){s=n[0];continue}else this.logger.warn(`missed to pass in variable ${r} for interpolating ${e}`),s=""}else M(s)||this.useRawValueToEscape||(s=F(s));let o=t.safeValue(s);if(e=e.replace(n[0],o),c?(t.regex.lastIndex+=s.length,t.regex.lastIndex-=n[0].length):t.regex.lastIndex=0,++a>=this.maxReplaces)break}}),e}nest(e,t){let r,i,n,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=(e,t)=>{let r=this.nestingOptionsSeparator;if(0>e.indexOf(r))return e;let i=e.split(RegExp(`${r}[ ]*{`)),s=`{${i[1]}`;e=i[0],s=this.interpolate(s,n);let a=s.match(/'/g),o=s.match(/"/g);(a&&a.length%2==0&&!o||o.length%2!=0)&&(s=s.replace(/'/g,'"'));try{n=JSON.parse(s),t&&(n={...t,...n})}catch(t){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,t),`${e}${r}${s}`}return n.defaultValue&&n.defaultValue.indexOf(this.prefix)>-1&&delete n.defaultValue,e};for(;r=this.nestingRegexp.exec(e);){let o=[];(n=(n={...s}).replace&&!M(n.replace)?n.replace:n).applyPostProcessor=!1,delete n.defaultValue;let l=!1;if(-1!==r[0].indexOf(this.formatSeparator)&&!/{.*}/.test(r[1])){let e=r[1].split(this.formatSeparator).map(e=>e.trim());r[1]=e.shift(),o=e,l=!0}if((i=t(a.call(this,r[1].trim(),n),n))&&r[0]===e&&!M(i))return i;M(i)||(i=F(i)),i||(this.logger.warn(`missed to resolve ${r[1]} for nesting ${e}`),i=""),l&&(i=o.reduce((e,t)=>this.format(e,t,s.lng,{...s,interpolationkey:r[1].trim()}),i.trim())),e=e.replace(r[0],i),this.regexp.lastIndex=0}return e}}let eP=e=>{let t=e.toLowerCase().trim(),r={};if(e.indexOf("(")>-1){let i=e.split("(");t=i[0].toLowerCase().trim();let n=i[1].substring(0,i[1].length-1);if("currency"===t&&0>n.indexOf(":"))r.currency||(r.currency=n.trim());else if("relativetime"===t&&0>n.indexOf(":"))r.range||(r.range=n.trim());else{let e=n.split(";");e.forEach(e=>{if(e){let[t,...i]=e.split(":"),n=i.join(":").trim().replace(/^'+|'+$/g,""),s=t.trim();r[s]||(r[s]=n),"false"===n&&(r[s]=!1),"true"===n&&(r[s]=!0),isNaN(n)||(r[s]=parseInt(n,10))}})}}return{formatName:t,formatOptions:r}},eE=e=>{let t={};return(r,i,n)=>{let s=n;n&&n.interpolationkey&&n.formatParams&&n.formatParams[n.interpolationkey]&&n[n.interpolationkey]&&(s={...s,[n.interpolationkey]:void 0});let a=i+JSON.stringify(s),o=t[a];return o||(o=e(ei(i),n),t[a]=o),o(r)}};class eC{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=ea.create("formatter"),this.options=e,this.formats={number:eE((e,t)=>{let r=new Intl.NumberFormat(e,{...t});return e=>r.format(e)}),currency:eE((e,t)=>{let r=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>r.format(e)}),datetime:eE((e,t)=>{let r=new Intl.DateTimeFormat(e,{...t});return e=>r.format(e)}),relativetime:eE((e,t)=>{let r=new Intl.RelativeTimeFormat(e,{...t});return e=>r.format(e,t.range||"day")}),list:eE((e,t)=>{let r=new Intl.ListFormat(e,{...t});return e=>r.format(e)})},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=eE(t)}format(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=t.split(this.formatSeparator);if(n.length>1&&n[0].indexOf("(")>1&&0>n[0].indexOf(")")&&n.find(e=>e.indexOf(")")>-1)){let e=n.findIndex(e=>e.indexOf(")")>-1);n[0]=[n[0],...n.splice(1,e)].join(this.formatSeparator)}let s=n.reduce((e,t)=>{let{formatName:n,formatOptions:s}=eP(t);if(this.formats[n]){let t=e;try{let a=i&&i.formatParams&&i.formatParams[i.interpolationkey]||{},o=a.locale||a.lng||i.locale||i.lng||r;t=this.formats[n](e,o,{...s,...i,...a})}catch(e){this.logger.warn(e)}return t}return this.logger.warn(`there was no format function for ${n}`),e},e);return s}}let eL=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)};class ej extends eo{constructor(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=r,this.languageUtils=r.languageUtils,this.options=i,this.logger=ea.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(r,i.backend,i)}queueLoad(e,t,r,i){let n={},s={},a={},o={};return e.forEach(e=>{let i=!0;t.forEach(t=>{let a=`${e}|${t}`;!r.reload&&this.store.hasResourceBundle(e,t)?this.state[a]=2:this.state[a]<0||(1===this.state[a]?void 0===s[a]&&(s[a]=!0):(this.state[a]=1,i=!1,void 0===s[a]&&(s[a]=!0),void 0===n[a]&&(n[a]=!0),void 0===o[t]&&(o[t]=!0)))}),i||(a[e]=!0)}),(Object.keys(n).length||Object.keys(s).length)&&this.queue.push({pending:s,pendingCount:Object.keys(s).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(n),pending:Object.keys(s),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(o)}}loaded(e,t,r){let i=e.split("|"),n=i[0],s=i[1];t&&this.emit("failedLoading",n,s,t),!t&&r&&this.store.addResourceBundle(n,s,r,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&r&&(this.state[e]=0);let a={};this.queue.forEach(r=>{H(r.loaded,[n],s),eL(r,e),t&&r.errors.push(t),0!==r.pendingCount||r.done||(Object.keys(r.loaded).forEach(e=>{a[e]||(a[e]={});let t=r.loaded[e];t.length&&t.forEach(t=>{void 0===a[e][t]&&(a[e][t]=!0)})}),r.done=!0,r.errors.length?r.callback(r.errors):r.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(e=>!e.done)}read(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,s=arguments.length>5?arguments[5]:void 0;if(!e.length)return s(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:r,tried:i,wait:n,callback:s});return}this.readingCalls++;let a=(a,o)=>{if(this.readingCalls--,this.waitingReads.length>0){let e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}if(a&&o&&i<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,r,i+1,2*n,s)},n);return}s(a,o)},o=this.backend[r].bind(this.backend);if(2===o.length){try{let r=o(e,t);r&&"function"==typeof r.then?r.then(e=>a(null,e)).catch(a):a(null,r)}catch(e){a(e)}return}return o(e,t,a)}prepareLoading(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),i&&i();M(e)&&(e=this.languageUtils.toResolveHierarchy(e)),M(t)&&(t=[t]);let n=this.queueLoad(e,t,r,i);if(!n.toLoad.length)return n.pending.length||i(),null;n.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,r){this.prepareLoading(e,t,{},r)}reload(e,t,r){this.prepareLoading(e,t,{reload:!0},r)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=e.split("|"),i=r[0],n=r[1];this.read(i,n,"read",void 0,void 0,(r,s)=>{r&&this.logger.warn(`${t}loading namespace ${n} for language ${i} failed`,r),!r&&s&&this.logger.log(`${t}loaded namespace ${n} for language ${i}`,s),this.loaded(e,r,s)})}saveMissing(e,t,r,i,n){let s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t)){this.logger.warn(`did not save key "${r}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(null!=r&&""!==r){if(this.backend&&this.backend.create){let o={...s,isUpdate:n},l=this.backend.create.bind(this.backend);if(l.length<6)try{let n;(n=5===l.length?l(e,t,r,i,o):l(e,t,r,i))&&"function"==typeof n.then?n.then(e=>a(null,e)).catch(a):a(null,n)}catch(e){a(e)}else l(e,t,r,i,a,o)}e&&e[0]&&this.store.addResource(e[0],t,r,i)}}}let eT=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),M(e[1])&&(t.defaultValue=e[1]),M(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){let r=e[3]||e[2];Object.keys(r).forEach(e=>{t[e]=r[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),eA=e=>(M(e.ns)&&(e.ns=[e.ns]),M(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),M(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&0>e.supportedLngs.indexOf("cimode")&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e),eO=()=>{},eR=e=>{let t=Object.getOwnPropertyNames(Object.getPrototypeOf(e));t.forEach(t=>{"function"==typeof e[t]&&(e[t]=e[t].bind(e))})};class eN extends eo{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=eA(e),this.services={},this.logger=ea,this.modules={external:[]},eR(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof t&&(r=t,t={}),!t.defaultNS&&!1!==t.defaultNS&&t.ns&&(M(t.ns)?t.defaultNS=t.ns:0>t.ns.indexOf("translation")&&(t.defaultNS=t.ns[0]));let i=eT();this.options={...i,...this.options,...eA(t)},"v1"!==this.options.compatibilityAPI&&(this.options.interpolation={...i.interpolation,...this.options.interpolation}),void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);let n=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?ea.init(n(this.modules.logger),this.options):ea.init(null,this.options),this.modules.formatter?t=this.modules.formatter:"undefined"!=typeof Intl&&(t=eC);let r=new ep(this.options);this.store=new el(this.options.resources,this.options);let s=this.services;s.logger=ea,s.resourceStore=this.store,s.languageUtils=r,s.pluralResolver=new ex(r,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),t&&(!this.options.interpolation.format||this.options.interpolation.format===i.interpolation.format)&&(s.formatter=n(t),s.formatter.init(s,this.options),this.options.interpolation.format=s.formatter.format.bind(s.formatter)),s.interpolator=new ek(this.options),s.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},s.backendConnector=new ej(n(this.modules.backend),s.resourceStore,s,this.options),s.backendConnector.on("*",function(t){for(var r=arguments.length,i=Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];e.emit(t,...i)}),this.modules.languageDetector&&(s.languageDetector=n(this.modules.languageDetector),s.languageDetector.init&&s.languageDetector.init(s,this.options.detection,this.options)),this.modules.i18nFormat&&(s.i18nFormat=n(this.modules.i18nFormat),s.i18nFormat.init&&s.i18nFormat.init(this)),this.translator=new eh(this.services,this.options),this.translator.on("*",function(t){for(var r=arguments.length,i=Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];e.emit(t,...i)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,r||(r=eO),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=function(){return e.store[t](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=function(){return e.store[t](...arguments),e}});let s=$(),a=()=>{let e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),s.resolve(t),r(e,t)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?a():setTimeout(a,0),s}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eO,r=t,i=M(e)?e:this.language;if("function"==typeof e&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if(i&&"cimode"===i.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return r();let e=[],t=t=>{if(!t||"cimode"===t)return;let r=this.services.languageUtils.toResolveHierarchy(t);r.forEach(t=>{"cimode"!==t&&0>e.indexOf(t)&&e.push(t)})};if(i)t(i);else{let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.forEach(e=>t(e))}this.options.preload&&this.options.preload.forEach(e=>t(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),r(e)})}else r(null)}reloadResources(e,t,r){let i=$();return"function"==typeof e&&(r=e,e=void 0),"function"==typeof t&&(r=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),r||(r=eO),this.services.backendConnector.reload(e,t,e=>{i.resolve(),r(e)}),i}use(e){if(!e)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&eu.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let e=0;e<this.languages.length;e++){let t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}}changeLanguage(e,t){var r=this;this.isLanguageChangingTo=e;let i=$();this.emit("languageChanging",e);let n=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},s=(e,s)=>{s?(n(s),this.translator.changeLanguage(s),this.isLanguageChangingTo=void 0,this.emit("languageChanged",s),this.logger.log("languageChanged",s)):this.isLanguageChangingTo=void 0,i.resolve(function(){return r.t(...arguments)}),t&&t(e,function(){return r.t(...arguments)})},a=t=>{e||t||!this.services.languageDetector||(t=[]);let r=M(t)?t:this.services.languageUtils.getBestMatchFromCodes(t);r&&(this.language||n(r),this.translator.language||this.translator.changeLanguage(r),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(r)),this.loadResources(r,e=>{s(e,r)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(a):this.services.languageDetector.detect(a):a(e):a(this.services.languageDetector.detect()),i}getFixedT(e,t,r){var i=this;let n=function(e,t){let s,a;if("object"!=typeof t){for(var o=arguments.length,l=Array(o>2?o-2:0),u=2;u<o;u++)l[u-2]=arguments[u];s=i.options.overloadTranslationOptionHandler([e,t].concat(l))}else s={...t};s.lng=s.lng||n.lng,s.lngs=s.lngs||n.lngs,s.ns=s.ns||n.ns,""!==s.keyPrefix&&(s.keyPrefix=s.keyPrefix||r||n.keyPrefix);let c=i.options.keySeparator||".";return a=s.keyPrefix&&Array.isArray(e)?e.map(e=>`${s.keyPrefix}${c}${e}`):s.keyPrefix?`${s.keyPrefix}${c}${e}`:e,i.t(a,s)};return M(e)?n.lng=e:n.lngs=e,n.ns=t,n.keyPrefix=r,n}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let r=t.lng||this.resolvedLanguage||this.languages[0],i=!!this.options&&this.options.fallbackLng,n=this.languages[this.languages.length-1];if("cimode"===r.toLowerCase())return!0;let s=(e,t)=>{let r=this.services.backendConnector.state[`${e}|${t}`];return -1===r||0===r||2===r};if(t.precheck){let e=t.precheck(this,s);if(void 0!==e)return e}return!!(this.hasResourceBundle(r,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||s(r,e)&&(!i||s(n,e)))}loadNamespaces(e,t){let r=$();return this.options.ns?(M(e)&&(e=[e]),e.forEach(e=>{0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}),this.loadResources(e=>{r.resolve(),t&&t(e)}),r):(t&&t(),Promise.resolve())}loadLanguages(e,t){let r=$();M(e)&&(e=[e]);let i=this.options.preload||[],n=e.filter(e=>0>i.indexOf(e)&&this.services.languageUtils.isSupportedCode(e));return n.length?(this.options.preload=i.concat(n),this.loadResources(e=>{r.resolve(),t&&t(e)}),r):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";let t=this.services&&this.services.languageUtils||new ep(eT());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new eN(e,t)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:eO,r=e.forkResourceStore;r&&delete e.forkResourceStore;let i={...this.options,...e,isClone:!0},n=new eN(i);return(void 0!==e.debug||void 0!==e.prefix)&&(n.logger=n.logger.clone(e)),["store","services","language"].forEach(e=>{n[e]=this[e]}),n.services={...this.services},n.services.utils={hasLoadedNamespace:n.hasLoadedNamespace.bind(n)},r&&(n.store=new el(this.store.data,i),n.services.resourceStore=n.store),n.translator=new eh(n.services,i),n.translator.on("*",function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];n.emit(e,...r)}),n.init(i,t),n.translator.options=i,n.translator.backendConnector.services.utils={hasLoadedNamespace:n.hasLoadedNamespace.bind(n)},n}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}let eV=eN.createInstance();eV.createInstance=eN.createInstance,eV.createInstance,eV.dir,eV.init,eV.loadResources,eV.reloadResources,eV.use,eV.changeLanguage,eV.getFixedT,eV.t,eV.exists,eV.setDefaultNamespace,eV.hasLoadedNamespace,eV.loadNamespaces,eV.loadLanguages;var eD=function(e){void 0===e.ns&&(e.ns=[]);var t,r,i=eV.createInstance(e);return i.isInitialized?t=Promise.resolve(eV.t):(null==e||null===(r=e.use)||void 0===r||r.forEach(function(e){return i.use(e)}),"function"==typeof e.onPreInitI18next&&e.onPreInitI18next(i),t=i.init(e)),{i18n:i,initPromise:t}},eM=n.createElement;function e$(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function eF(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?e$(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):e$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eI=function(e,t){if(t&&e.isInitialized)for(var r=0,i=Object.keys(t);r<i.length;r++)for(var n=i[r],s=0,a=Object.keys(t[n]);s<a.length;s++){var o,l=a[s];null!=e&&null!==(o=e.store)&&void 0!==o&&o.data&&e.store.data[n]&&e.store.data[n][l]||e.addResourceBundle(n,l,t[n][l],!0,!0)}},eB=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return k()(function(r){var i,s,a=(r.pageProps||{})._nextI18Next,o=null!==(i=null==a?void 0:a.initialLocale)&&void 0!==i?i:null==r||null===(s=r.router)||void 0===s?void 0:s.locale,l=null==a?void 0:a.ns,u=(0,n.useRef)(null),c=(0,n.useMemo)(function(){if(!a&&!t)return null;var e,r=null!=t?t:null==a?void 0:a.userConfig;if(!r)throw Error("appWithTranslation was called without a next-i18next config");if(!(null!=r&&r.i18n))throw Error("appWithTranslation was called without config.i18n");if(!(null!=r&&null!==(e=r.i18n)&&void 0!==e&&e.defaultLocale))throw Error("config.i18n does not include a defaultLocale property");var i=(a||{}).initialI18nStore,n=null!=t&&t.resources?t.resources:i;o||(o=r.i18n.defaultLocale);var s=u.current;return s?eI(s,n):(eI(s=eD(eF(eF(eF({},D(eF(eF({},r),{},{lng:o}))),{},{lng:o},l&&{ns:l}),{},{resources:n})).i18n,n),u.current=s),s},[a,o,l]);return T(function(){c&&o&&c.changeLanguage(o)},[c,o]),null!==c?eM(v,{i18n:c},eM(e,r)):eM(e,b({key:o},r))},e)}},6570:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return r(64)}])},64:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return I}});var i=r(2322),n=r(3592),s=r.n(n),a=r(9288),o=r.n(a),l=r(6526),u=r.n(l),c=r(3785),h=r.n(c),d=r(3340),p=r.n(d),f=r(34),m=r(2784);let g=["light","dark"],y="(prefers-color-scheme: dark)",v="undefined"==typeof window,b=(0,m.createContext)(void 0),x=e=>(0,m.useContext)(b)?m.createElement(m.Fragment,null,e.children):m.createElement(S,e),w=["light","dark"],S=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:r=!0,enableColorScheme:i=!0,storageKey:n="theme",themes:s=w,defaultTheme:a=r?"system":"light",attribute:o="data-theme",value:l,children:u,nonce:c})=>{let[h,d]=(0,m.useState)(()=>P(n,a)),[p,f]=(0,m.useState)(()=>P(n)),v=l?Object.values(l):s,x=(0,m.useCallback)(e=>{let n=e;if(!n)return;"system"===e&&r&&(n=C());let s=l?l[n]:n,u=t?E():null,c=document.documentElement;if("class"===o?(c.classList.remove(...v),s&&c.classList.add(s)):s?c.setAttribute(o,s):c.removeAttribute(o),i){let e=g.includes(a)?a:null,t=g.includes(n)?n:e;c.style.colorScheme=t}null==u||u()},[]),S=(0,m.useCallback)(e=>{d(e);try{localStorage.setItem(n,e)}catch(e){}},[e]),L=(0,m.useCallback)(t=>{let i=C(t);f(i),"system"===h&&r&&!e&&x("system")},[h,e]);(0,m.useEffect)(()=>{let e=window.matchMedia(y);return e.addListener(L),L(e),()=>e.removeListener(L)},[L]),(0,m.useEffect)(()=>{let e=e=>{e.key===n&&S(e.newValue||a)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[S]),(0,m.useEffect)(()=>{x(null!=e?e:h)},[e,h]);let j=(0,m.useMemo)(()=>({theme:h,setTheme:S,forcedTheme:e,resolvedTheme:"system"===h?p:h,themes:r?[...s,"system"]:s,systemTheme:r?p:void 0}),[h,S,e,p,r,s]);return m.createElement(b.Provider,{value:j},m.createElement(k,{forcedTheme:e,disableTransitionOnChange:t,enableSystem:r,enableColorScheme:i,storageKey:n,themes:s,defaultTheme:a,attribute:o,value:l,children:u,attrs:v,nonce:c}),u)},k=(0,m.memo)(({forcedTheme:e,storageKey:t,attribute:r,enableSystem:i,enableColorScheme:n,defaultTheme:s,value:a,attrs:o,nonce:l})=>{let u="system"===s,c="class"===r?`var d=document.documentElement,c=d.classList;c.remove(${o.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${r}',s='setAttribute';`,h=n?g.includes(s)&&s?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${s}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",d=(e,t=!1,i=!0)=>{let s=a?a[e]:e,o=t?e+"|| ''":`'${s}'`,l="";return n&&i&&!t&&g.includes(e)&&(l+=`d.style.colorScheme = '${e}';`),"class"===r?l+=t||s?`c.add(${o})`:"null":s&&(l+=`d[s](n,${o})`),l},p=e?`!function(){${c}${d(e)}}()`:i?`!function(){try{${c}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${u})){var t='${y}',m=window.matchMedia(t);if(m.media!==t||m.matches){${d("dark")}}else{${d("light")}}}else if(e){${a?`var x=${JSON.stringify(a)};`:""}${d(a?"x[e]":"e",!0)}}${u?"":"else{"+d(s,!1,!1)+"}"}${h}}catch(e){}}()`:`!function(){try{${c}var e=localStorage.getItem('${t}');if(e){${a?`var x=${JSON.stringify(a)};`:""}${d(a?"x[e]":"e",!0)}}else{${d(s,!1,!1)};}${h}}catch(t){}}();`;return m.createElement("script",{nonce:l,dangerouslySetInnerHTML:{__html:p}})},()=>!0),P=(e,t)=>{let r;if(!v){try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t}},E=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},C=e=>(e||(e=window.matchMedia(y)),e.matches?"dark":"light");var L=r(2202),j=r(5632);class T extends m.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){var r,i,n,s,a,o;let l=(null===(r=e.message)||void 0===r?void 0:r.includes("Frame with ID"))||(null===(i=e.message)||void 0===i?void 0:i.includes("Could not establish connection"))||(null===(n=e.message)||void 0===n?void 0:n.includes("MetaMask"))||(null===(s=e.message)||void 0===s?void 0:s.includes("chrome-extension"))||(null===(a=e.message)||void 0===a?void 0:a.includes("contentscript"))||(null===(o=e.message)||void 0===o?void 0:o.includes("serviceWorker"));l||console.error("ErrorBoundary caught an error:",e,t)}render(){if(this.state.hasError){var e,t,r,n,s,a,o,l,u,c,h,d;let p=(null===(t=this.state.error)||void 0===t?void 0:null===(e=t.message)||void 0===e?void 0:e.includes("Frame with ID"))||(null===(n=this.state.error)||void 0===n?void 0:null===(r=n.message)||void 0===r?void 0:r.includes("Could not establish connection"))||(null===(a=this.state.error)||void 0===a?void 0:null===(s=a.message)||void 0===s?void 0:s.includes("MetaMask"))||(null===(l=this.state.error)||void 0===l?void 0:null===(o=l.message)||void 0===o?void 0:o.includes("chrome-extension"))||(null===(c=this.state.error)||void 0===c?void 0:null===(u=c.message)||void 0===u?void 0:u.includes("contentscript"))||(null===(d=this.state.error)||void 0===d?void 0:null===(h=d.message)||void 0===h?void 0:h.includes("serviceWorker"));return p?this.props.children:this.props.fallback||(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"Something went wrong"}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"We're sorry, but something unexpected happened. Please refresh the page."}),(0,i.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"btn-primary",children:"Refresh Page"})]})})}return this.props.children}constructor(e){super(e),this.state={hasError:!1}}}var A=r(1675),O=r(5239),R=r(8508);let N=m.forwardRef(function({title:e,titleId:t,...r},i){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.098 19.902a3.75 3.75 0 0 0 5.304 0l6.401-6.402M6.75 21A3.75 3.75 0 0 1 3 17.25V4.125C3 3.504 3.504 3 4.125 3h5.25c.621 0 1.125.504 1.125 1.125v4.072M6.75 21a3.75 3.75 0 0 0 3.75-3.75V8.197M6.75 21h13.125c.621 0 1.125-.504 1.125-1.125v-5.25c0-.621-.504-1.125-1.125-1.125h-4.072M10.5 8.197l2.88-2.88c.438-.439 1.15-.439 1.59 0l3.712 3.713c.44.44.44 1.152 0 1.59l-2.879 2.88M6.75 17.25h.008v.008H6.75v-.008Z"}))});var V=r(5116),D=r(6763);let M=m.forwardRef(function({title:e,titleId:t,...r},i){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z"}))}),$=m.forwardRef(function({title:e,titleId:t,...r},i){return m.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},r),e?m.createElement("title",{id:t},e):null,m.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))});var F=e=>{let{showInProduction:t=!1,position:r="bottom-right"}=e,{switchTheme:n,isGoldTheme:s,isPurpleTheme:a}=(0,A.Fg)(),[o,l]=(0,m.useState)(!1),[u,c]=(0,m.useState)(!1);if((0,m.useEffect)(()=>{c(t)},[t]),!u)return null;let h=e=>{n(e),setTimeout(()=>l(!1),500)};return(0,i.jsxs)("div",{className:"fixed ".concat({"bottom-right":"bottom-6 right-6","bottom-left":"bottom-6 left-6","top-right":"top-6 right-6","top-left":"top-6 left-6"}[r]," z-[9999] select-none"),children:[(0,i.jsx)(O.M,{children:o&&(0,i.jsxs)(R.E.div,{initial:{opacity:0,y:20,scale:.9},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:20,scale:.9},transition:{duration:.2,ease:"easeOut"},className:"mb-4 p-4 rounded-2xl overflow-hidden",style:{background:"linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%)",backdropFilter:"blur(25px)",WebkitBackdropFilter:"blur(25px)",border:"1px solid rgba(255, 255, 255, 0.1)",boxShadow:"0 25px 50px rgba(0, 0, 0, 0.5)"},children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(N,{className:"w-5 h-5 text-white"}),(0,i.jsx)("span",{className:"text-white font-semibold text-sm",children:"Theme Controller"})]}),(0,i.jsx)("button",{onClick:()=>l(!1),className:"text-white/60 hover:text-white transition-colors p-1",children:(0,i.jsx)(V.Z,{className:"w-4 h-4"})})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(R.E.button,{onClick:()=>h("gold"),className:"w-full p-3 rounded-xl transition-all duration-300 group ".concat(s?"ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-500/20 to-orange-500/20":"hover:bg-white/5"),whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"w-8 h-8 rounded-lg overflow-hidden",style:{background:"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)",boxShadow:"0 4px 12px rgba(255, 215, 0, 0.3)"}}),s&&(0,i.jsx)("div",{className:"absolute -top-1 -right-1",children:(0,i.jsx)(D.Z,{className:"w-4 h-4 text-yellow-400"})})]}),(0,i.jsxs)("div",{className:"flex-1 text-left",children:[(0,i.jsx)("div",{className:"text-white font-medium text-sm",children:"Gold Premium"}),(0,i.jsx)("div",{className:"text-white/60 text-xs",children:"Luxury & Elegance"})]}),s&&(0,i.jsx)("div",{className:"text-yellow-400",children:(0,i.jsx)(M,{className:"w-4 h-4"})})]})}),(0,i.jsx)(R.E.button,{onClick:()=>h("purple"),className:"w-full p-3 rounded-xl transition-all duration-300 group ".concat(a?"ring-2 ring-purple-400 bg-gradient-to-r from-purple-500/20 to-blue-500/20":"hover:bg-white/5"),whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"w-8 h-8 rounded-lg overflow-hidden",style:{background:"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)",boxShadow:"0 4px 12px rgba(217, 70, 239, 0.3)"}}),a&&(0,i.jsx)("div",{className:"absolute -top-1 -right-1",children:(0,i.jsx)(D.Z,{className:"w-4 h-4 text-purple-400"})})]}),(0,i.jsxs)("div",{className:"flex-1 text-left",children:[(0,i.jsx)("div",{className:"text-white font-medium text-sm",children:"Purple Dark"}),(0,i.jsx)("div",{className:"text-white/60 text-xs",children:"Modern & Professional"})]}),a&&(0,i.jsx)("div",{className:"text-purple-400",children:(0,i.jsx)(M,{className:"w-4 h-4"})})]})})]}),(0,i.jsx)("div",{className:"mt-4 pt-3 border-t border-white/10",children:(0,i.jsxs)("div",{className:"text-white/40 text-xs text-center",children:["Press ",(0,i.jsx)("kbd",{className:"px-1 py-0.5 bg-white/10 rounded text-white/60",children:"Ctrl+Shift+T"})," to toggle"]})})]})}),(0,i.jsxs)(R.E.button,{onClick:()=>l(!o),className:"relative p-3 rounded-full overflow-hidden group",style:{background:s?"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)":"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)",boxShadow:s?"0 8px 25px rgba(255, 215, 0, 0.4)":"0 8px 25px rgba(217, 70, 239, 0.4)"},whileHover:{scale:1.1},whileTap:{scale:.95},animate:{rotate:o?180:0},transition:{duration:.3},children:[(0,i.jsx)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500",style:{background:"linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)",backgroundSize:"200% 100%",animation:"shimmer 2s ease-in-out infinite"}}),(0,i.jsx)("div",{className:"relative z-10",children:o?(0,i.jsx)($,{className:"w-6 h-6 text-white"}):(0,i.jsx)(N,{className:"w-6 h-6 text-white"})}),(0,i.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 rounded-full bg-white shadow-lg"})]}),(0,i.jsx)(O.M,{children:!o&&(0,i.jsx)(R.E.div,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},exit:{opacity:0,x:-10},className:"absolute right-full mr-3 top-1/2 -translate-y-1/2 px-2 py-1 rounded-md text-xs text-white whitespace-nowrap pointer-events-none",style:{background:"rgba(0, 0, 0, 0.8)",backdropFilter:"blur(10px)"},children:s?"Gold Theme":"Purple Theme"})})]})};r(3596);var I=(0,f.Jc)(function(e){let{Component:t,pageProps:r}=e,n=(0,j.useRouter)(),{locale:a}=n,l="ar"===a;return(0,m.useEffect)(()=>{document.documentElement.dir=l?"rtl":"ltr",document.documentElement.lang=a||"ar"},[a,l]),(0,i.jsx)(T,{children:(0,i.jsx)("div",{className:"".concat(s().variable," ").concat(o().variable," ").concat(u().variable," ").concat(h().variable," ").concat(p().variable," ").concat(l?"font-cairo":"font-sans"),children:(0,i.jsx)(A.f6,{defaultTheme:"gold",children:(0,i.jsxs)(x,{attribute:"class",defaultTheme:"dark",enableSystem:!1,disableTransitionOnChange:!1,children:[(0,i.jsx)(t,{...r}),(0,i.jsx)(F,{}),(0,i.jsx)(L.x7,{position:l?"top-left":"top-right",toastOptions:{duration:4e3,style:{background:"var(--toast-bg)",color:"var(--toast-color)",direction:l?"rtl":"ltr"}}})]})})})})})},1675:function(e,t,r){"use strict";r.d(t,{f6:function(){return S},Fg:function(){return k}});var i=r(2322),n=r(2784);let s={name:"gold",displayName:"Gold Premium",colors:{primary:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},secondary:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},accent:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},glass:{background:"rgba(255, 215, 0, 0.12)",border:"rgba(255, 215, 0, 0.25)",shadow:"0 8px 32px rgba(255, 215, 0, 0.15)",backdropBlur:"blur(25px)"},text:{primary:"#ffffff",secondary:"rgba(255, 255, 255, 0.8)",accent:"#FFD700",muted:"rgba(255, 255, 255, 0.6)"}},backgrounds:{primary:"\n      radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.08) 0%, transparent 60%),\n      linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%)\n    ",secondary:"\n      radial-gradient(ellipse at top, rgba(184, 134, 11, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\n    ",tertiary:"\n      radial-gradient(ellipse at bottom, rgba(139, 105, 20, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)\n    "},gradients:{primary:"linear-gradient(135deg, #FFD700 0%, #B8860B 100%)",secondary:"linear-gradient(135deg, #FFA500 0%, #FF8C00 100%)",accent:"linear-gradient(135deg, #DAA520 0%, #B8860B 100%)",text:"linear-gradient(90deg,\n      #8B6914 0%, #B8860B 10%, #DAA520 20%, #FFD700 30%,\n      #FFED4E 40%, #FFF8DC 50%, #FFED4E 60%, #FFD700 70%,\n      #DAA520 80%, #B8860B 90%, #8B6914 100%)",button:"linear-gradient(135deg, #FFD700 0%, #B8860B 50%, #DAA520 100%)",card:"linear-gradient(135deg, rgba(255, 215, 0, 0.08) 0%, rgba(255, 215, 0, 0.04) 100%)"},shadows:{sm:"0 2px 4px rgba(255, 215, 0, 0.1)",md:"0 4px 8px rgba(255, 215, 0, 0.15)",lg:"0 8px 16px rgba(255, 215, 0, 0.2)",xl:"0 12px 24px rgba(255, 215, 0, 0.25)",glass:"0 8px 32px rgba(255, 215, 0, 0.15)",premium:"0 25px 50px rgba(255, 215, 0, 0.3)"},animations:{shimmer:"\n      @keyframes goldShimmer {\n        0%, 100% { background-position: 0% 50%; }\n        50% { background-position: 100% 50%; }\n      }\n    ",glow:"\n      @keyframes goldGlow {\n        0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }\n        50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.6); }\n      }\n    ",pulse:"\n      @keyframes goldPulse {\n        0%, 100% { opacity: 1; }\n        50% { opacity: 0.7; }\n      }\n    ",float:"\n      @keyframes goldFloat {\n        0%, 100% { transform: translateY(0px); }\n        50% { transform: translateY(-10px); }\n      }\n    "}},a={"--theme-primary-50":s.colors.primary[50],"--theme-primary-100":s.colors.primary[100],"--theme-primary-200":s.colors.primary[200],"--theme-primary-300":s.colors.primary[300],"--theme-primary-400":s.colors.primary[400],"--theme-primary-500":s.colors.primary[500],"--theme-primary-600":s.colors.primary[600],"--theme-primary-700":s.colors.primary[700],"--theme-primary-800":s.colors.primary[800],"--theme-primary-900":s.colors.primary[900],"--theme-glass-bg":s.colors.glass.background,"--theme-glass-border":s.colors.glass.border,"--theme-glass-shadow":s.colors.glass.shadow,"--theme-glass-blur":s.colors.glass.backdropBlur,"--theme-bg-primary":s.backgrounds.primary,"--theme-bg-secondary":s.backgrounds.secondary,"--theme-bg-tertiary":s.backgrounds.tertiary||s.backgrounds.secondary,"--theme-text-primary":s.colors.text.primary,"--theme-text-secondary":s.colors.text.secondary,"--theme-text-accent":s.colors.text.accent,"--theme-text-muted":s.colors.text.muted,"--theme-gradient-primary":s.gradients.primary,"--theme-gradient-secondary":s.gradients.secondary,"--theme-gradient-accent":s.gradients.accent,"--theme-gradient-text":s.gradients.text,"--theme-gradient-button":s.gradients.button,"--theme-gradient-card":s.gradients.card,"--theme-shadow-sm":s.shadows.sm,"--theme-shadow-md":s.shadows.md,"--theme-shadow-lg":s.shadows.lg,"--theme-shadow-xl":s.shadows.xl,"--theme-shadow-glass":s.shadows.glass,"--theme-shadow-premium":s.shadows.premium},o={name:"purple",displayName:"Purple Dark",colors:{primary:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},secondary:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},accent:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},glass:{background:"rgba(217, 70, 239, 0.12)",border:"rgba(217, 70, 239, 0.25)",shadow:"0 8px 32px rgba(217, 70, 239, 0.15)",backdropBlur:"blur(25px)"},text:{primary:"#ffffff",secondary:"rgba(255, 255, 255, 0.8)",accent:"#d946ef",muted:"rgba(255, 255, 255, 0.6)"}},backgrounds:{primary:"\n      radial-gradient(circle at 50% 50%, rgba(217, 70, 239, 0.08) 0%, transparent 60%),\n      linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%)\n    ",secondary:"\n      radial-gradient(ellipse at top, rgba(192, 38, 211, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)\n    ",tertiary:"\n      radial-gradient(ellipse at bottom, rgba(162, 28, 175, 0.1) 0%, transparent 50%),\n      linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)\n    "},gradients:{primary:"linear-gradient(135deg, #d946ef 0%, #a21caf 100%)",secondary:"linear-gradient(135deg, #c026d3 0%, #86198f 100%)",accent:"linear-gradient(135deg, #e879f9 0%, #c026d3 100%)",text:"linear-gradient(90deg,\n      #701a75 0%, #86198f 10%, #a21caf 20%, #c026d3 30%,\n      #d946ef 40%, #e879f9 50%, #d946ef 60%, #c026d3 70%,\n      #a21caf 80%, #86198f 90%, #701a75 100%)",button:"linear-gradient(135deg, #d946ef 0%, #a21caf 50%, #86198f 100%)",card:"linear-gradient(135deg, rgba(217, 70, 239, 0.08) 0%, rgba(217, 70, 239, 0.04) 100%)"},shadows:{sm:"0 2px 4px rgba(217, 70, 239, 0.1)",md:"0 4px 8px rgba(217, 70, 239, 0.15)",lg:"0 8px 16px rgba(217, 70, 239, 0.2)",xl:"0 12px 24px rgba(217, 70, 239, 0.25)",glass:"0 8px 32px rgba(217, 70, 239, 0.15)",premium:"0 25px 50px rgba(217, 70, 239, 0.3)"},animations:{shimmer:"\n      @keyframes purpleShimmer {\n        0%, 100% { background-position: 0% 50%; }\n        50% { background-position: 100% 50%; }\n      }\n    ",glow:"\n      @keyframes purpleGlow {\n        0%, 100% { box-shadow: 0 0 20px rgba(217, 70, 239, 0.3); }\n        50% { box-shadow: 0 0 40px rgba(217, 70, 239, 0.6); }\n      }\n    ",pulse:"\n      @keyframes purplePulse {\n        0%, 100% { opacity: 1; }\n        50% { opacity: 0.7; }\n      }\n    ",float:"\n      @keyframes purpleFloat {\n        0%, 100% { transform: translateY(0px); }\n        50% { transform: translateY(-10px); }\n      }\n    "}},l={"--theme-primary-50":o.colors.primary[50],"--theme-primary-100":o.colors.primary[100],"--theme-primary-200":o.colors.primary[200],"--theme-primary-300":o.colors.primary[300],"--theme-primary-400":o.colors.primary[400],"--theme-primary-500":o.colors.primary[500],"--theme-primary-600":o.colors.primary[600],"--theme-primary-700":o.colors.primary[700],"--theme-primary-800":o.colors.primary[800],"--theme-primary-900":o.colors.primary[900],"--theme-glass-bg":o.colors.glass.background,"--theme-glass-border":o.colors.glass.border,"--theme-glass-shadow":o.colors.glass.shadow,"--theme-glass-blur":o.colors.glass.backdropBlur,"--theme-bg-primary":o.backgrounds.primary,"--theme-bg-secondary":o.backgrounds.secondary,"--theme-bg-tertiary":o.backgrounds.tertiary||o.backgrounds.secondary,"--theme-text-primary":o.colors.text.primary,"--theme-text-secondary":o.colors.text.secondary,"--theme-text-accent":o.colors.text.accent,"--theme-text-muted":o.colors.text.muted,"--theme-gradient-primary":o.gradients.primary,"--theme-gradient-secondary":o.gradients.secondary,"--theme-gradient-accent":o.gradients.accent,"--theme-gradient-text":o.gradients.text,"--theme-gradient-button":o.gradients.button,"--theme-gradient-card":o.gradients.card,"--theme-shadow-sm":o.shadows.sm,"--theme-shadow-md":o.shadows.md,"--theme-shadow-lg":o.shadows.lg,"--theme-shadow-xl":o.shadows.xl,"--theme-shadow-glass":o.shadows.glass,"--theme-shadow-premium":o.shadows.premium},u={gold:s,purple:o},c={gold:a,purple:l},h=e=>u[e],d=e=>c[e],p=e=>{let t=d(e),r=document.documentElement;r.classList.remove("theme-gold","theme-purple"),r.classList.add("theme-".concat(e)),Object.entries(t).forEach(e=>{let[t,i]=e;r.style.setProperty(t,i)})},f=()=>{try{let e=localStorage.getItem("freela-theme");return e&&("gold"===e||"purple"===e)?e:null}catch(e){return null}},m=e=>{try{localStorage.setItem("freela-theme",e)}catch(e){}},g=e=>"theme-".concat(e),y=()=>{if("undefined"==typeof document)return;let e=document.createElement("style");e.textContent="\n    * {\n      transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease !important;\n    }\n  ",document.head.appendChild(e),setTimeout(()=>{document.head.removeChild(e)},300)},v=e=>{let t=h(e),r=d(e);return"\n    .theme-".concat(e," {\n      ").concat(Object.entries(r).map(e=>{let[t,r]=e;return"".concat(t,": ").concat(r,";")}).join("\n      "),"\n    }\n    \n    /* Theme-specific animations */\n    ").concat(t.animations.shimmer,"\n    ").concat(t.animations.glow,"\n    ").concat(t.animations.pulse,"\n    ").concat(t.animations.float,"\n    \n    /* Theme-specific utilities */\n    .theme-").concat(e," .bg-theme-primary {\n      background: var(--theme-bg-primary);\n    }\n    \n    .theme-").concat(e," .bg-theme-secondary {\n      background: var(--theme-bg-secondary);\n    }\n    \n    .theme-").concat(e," .glass-effect {\n      background: var(--theme-glass-bg);\n      border: 1px solid var(--theme-glass-border);\n      box-shadow: var(--theme-glass-shadow);\n      backdrop-filter: var(--theme-glass-blur);\n      -webkit-backdrop-filter: var(--theme-glass-blur);\n    }\n    \n    .theme-").concat(e," .text-theme-gradient {\n      background: var(--theme-gradient-text);\n      background-size: 300% 100%;\n      -webkit-background-clip: text;\n      background-clip: text;\n      -webkit-text-fill-color: transparent;\n      animation: ").concat(e,"Shimmer 4s ease-in-out infinite;\n    }\n    \n    .theme-").concat(e," .btn-theme-primary {\n      background: var(--theme-gradient-button);\n      box-shadow: var(--theme-shadow-premium);\n      color: white;\n      border: 1px solid var(--theme-glass-border);\n    }\n    \n    .theme-").concat(e," .btn-theme-primary:hover {\n      animation: ").concat(e,"Glow 2s ease-in-out infinite;\n    }\n  ")},b=e=>{if("undefined"==typeof document)return;let t=document.getElementById("theme-".concat(e,"-styles"));t&&t.remove();let r=document.createElement("style");r.id="theme-".concat(e,"-styles"),r.textContent=v(e),document.head.appendChild(r)},x=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"gold",t=f(),r=t||e;return p(r),b(r),r},w=(0,n.createContext)(void 0),S=e=>{let{children:t,defaultTheme:r="gold"}=e,[s,a]=(0,n.useState)(r),[o,l]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=x(r);a(e),l(!0)},[r]);let u=h(s),c=(0,n.useCallback)(e=>{e!==s&&(y(),p(e),b(e),a(e),m(e),window.dispatchEvent(new CustomEvent("themeChanged",{detail:{theme:e}})))},[s]);(0,n.useEffect)(()=>{let e=e=>{if(e.ctrlKey&&e.shiftKey&&"T"===e.key){e.preventDefault();let t="gold"===s?"purple":"gold";c(t)}};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[s,c]);let d={currentTheme:u,themeName:s,switchTheme:c,isGoldTheme:"gold"===s,isPurpleTheme:"purple"===s,themeClasses:g(s)};return o?(0,i.jsx)(w.Provider,{value:d,children:(0,i.jsx)("div",{className:"theme-provider ".concat(g(s)),children:t})}):null},k=()=>{let e=(0,n.useContext)(w);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},3596:function(){},3785:function(e){e.exports={style:{fontFamily:"'__Cairo_baae29', '__Cairo_Fallback_baae29'",fontStyle:"normal"},className:"__className_baae29",variable:"__variable_baae29"}},3592:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},9288:function(e){e.exports={style:{fontFamily:"'__Noto_Sans_Arabic_386ca1', '__Noto_Sans_Arabic_Fallback_386ca1'",fontStyle:"normal"},className:"__className_386ca1",variable:"__variable_386ca1"}},6526:function(e){e.exports={style:{fontFamily:"'__Poppins_51684b', '__Poppins_Fallback_51684b'",fontStyle:"normal"},className:"__className_51684b",variable:"__variable_51684b"}},3340:function(e){e.exports={style:{fontFamily:"'__Tajawal_a9af04', '__Tajawal_Fallback_a9af04'",fontStyle:"normal"},className:"__className_a9af04",variable:"__variable_a9af04"}},5632:function(e,t,r){e.exports=r(5123)},6866:function(e,t){"use strict";/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r="function"==typeof Symbol&&Symbol.for,i=r?Symbol.for("react.element"):60103,n=r?Symbol.for("react.portal"):60106,s=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,o=r?Symbol.for("react.profiler"):60114,l=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,c=r?Symbol.for("react.async_mode"):60111,h=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,f=r?Symbol.for("react.suspense_list"):60120,m=r?Symbol.for("react.memo"):60115,g=r?Symbol.for("react.lazy"):60116,y=r?Symbol.for("react.block"):60121,v=r?Symbol.for("react.fundamental"):60117,b=r?Symbol.for("react.responder"):60118,x=r?Symbol.for("react.scope"):60119;function w(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case i:switch(e=e.type){case c:case h:case s:case o:case a:case p:return e;default:switch(e=e&&e.$$typeof){case u:case d:case g:case m:case l:return e;default:return t}}case n:return t}}}function S(e){return w(e)===h}t.AsyncMode=c,t.ConcurrentMode=h,t.ContextConsumer=u,t.ContextProvider=l,t.Element=i,t.ForwardRef=d,t.Fragment=s,t.Lazy=g,t.Memo=m,t.Portal=n,t.Profiler=o,t.StrictMode=a,t.Suspense=p,t.isAsyncMode=function(e){return S(e)||w(e)===c},t.isConcurrentMode=S,t.isContextConsumer=function(e){return w(e)===u},t.isContextProvider=function(e){return w(e)===l},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===i},t.isForwardRef=function(e){return w(e)===d},t.isFragment=function(e){return w(e)===s},t.isLazy=function(e){return w(e)===g},t.isMemo=function(e){return w(e)===m},t.isPortal=function(e){return w(e)===n},t.isProfiler=function(e){return w(e)===o},t.isStrictMode=function(e){return w(e)===a},t.isSuspense=function(e){return w(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===s||e===h||e===o||e===a||e===p||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===m||e.$$typeof===l||e.$$typeof===u||e.$$typeof===d||e.$$typeof===v||e.$$typeof===b||e.$$typeof===x||e.$$typeof===y)},t.typeOf=w},8570:function(e,t,r){"use strict";e.exports=r(6866)},4896:function(e){e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},6763:function(e,t,r){"use strict";var i=r(2784);let n=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))});t.Z=n},5116:function(e,t,r){"use strict";var i=r(2784);let n=i.forwardRef(function({title:e,titleId:t,...r},n){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});t.Z=n},5239:function(e,t,r){"use strict";r.d(t,{M:function(){return g}});var i=r(2784),n=r(3617);function s(){let e=(0,i.useRef)(!1);return(0,n.L)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var a=r(2972),o=r(7967),l=r(3105);class u extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function c({children:e,isPresent:t}){let r=(0,i.useId)(),n=(0,i.useRef)(null),s=(0,i.useRef)({width:0,height:0,top:0,left:0});return(0,i.useInsertionEffect)(()=>{let{width:e,height:i,top:a,left:o}=s.current;if(t||!n.current||!e||!i)return;n.current.dataset.motionPopId=r;let l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${i}px !important;
            top: ${a}px !important;
            left: ${o}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[t]),i.createElement(u,{isPresent:t,childRef:n,sizeRef:s},i.cloneElement(e,{ref:n}))}let h=({children:e,initial:t,isPresent:r,onExitComplete:n,custom:s,presenceAffectsLayout:a,mode:u})=>{let h=(0,l.h)(d),p=(0,i.useId)(),f=(0,i.useMemo)(()=>({id:p,initial:t,isPresent:r,custom:s,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;n&&n()},register:e=>(h.set(e,!1),()=>h.delete(e))}),a?void 0:[r]);return(0,i.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[r]),i.useEffect(()=>{r||h.size||!n||n()},[r]),"popLayout"===u&&(e=i.createElement(c,{isPresent:r},e)),i.createElement(o.O.Provider,{value:f},e)};function d(){return new Map}var p=r(3422),f=r(7035);let m=e=>e.key||"",g=({children:e,custom:t,initial:r=!0,onExitComplete:o,exitBeforeEnter:l,presenceAffectsLayout:u=!0,mode:c="sync"})=>{var d;(0,f.k)(!l,"Replace exitBeforeEnter with mode='wait'");let g=(0,i.useContext)(p.p).forceRender||function(){let e=s(),[t,r]=(0,i.useState)(0),n=(0,i.useCallback)(()=>{e.current&&r(t+1)},[t]),o=(0,i.useCallback)(()=>a.Wi.postRender(n),[n]);return[o,t]}()[0],y=s(),v=function(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}(e),b=v,x=(0,i.useRef)(new Map).current,w=(0,i.useRef)(b),S=(0,i.useRef)(new Map).current,k=(0,i.useRef)(!0);if((0,n.L)(()=>{k.current=!1,function(e,t){e.forEach(e=>{let r=m(e);t.set(r,e)})}(v,S),w.current=b}),d=()=>{k.current=!0,S.clear(),x.clear()},(0,i.useEffect)(()=>()=>d(),[]),k.current)return i.createElement(i.Fragment,null,b.map(e=>i.createElement(h,{key:m(e),isPresent:!0,initial:!!r&&void 0,presenceAffectsLayout:u,mode:c},e)));b=[...b];let P=w.current.map(m),E=v.map(m),C=P.length;for(let e=0;e<C;e++){let t=P[e];-1!==E.indexOf(t)||x.has(t)||x.set(t,void 0)}return"wait"===c&&x.size&&(b=[]),x.forEach((e,r)=>{if(-1!==E.indexOf(r))return;let n=S.get(r);if(!n)return;let s=P.indexOf(r),a=e;a||(a=i.createElement(h,{key:m(n),isPresent:!1,onExitComplete:()=>{x.delete(r);let e=Array.from(S.keys()).filter(e=>!E.includes(e));if(e.forEach(e=>S.delete(e)),w.current=v.filter(t=>{let i=m(t);return i===r||e.includes(i)}),!x.size){if(!1===y.current)return;g(),o&&o()}},custom:t,presenceAffectsLayout:u,mode:c},n),x.set(r,a)),b.splice(s,0,a)}),b=b.map(e=>{let t=e.key;return x.has(t)?e:i.createElement(h,{key:m(e),isPresent:!0,presenceAffectsLayout:u,mode:c},e)}),i.createElement(i.Fragment,null,x.size?b:b.map(e=>(0,i.cloneElement)(e)))}},3422:function(e,t,r){"use strict";r.d(t,{p:function(){return n}});var i=r(2784);let n=(0,i.createContext)({})},7967:function(e,t,r){"use strict";r.d(t,{O:function(){return n}});var i=r(2784);let n=(0,i.createContext)(null)},2972:function(e,t,r){"use strict";r.d(t,{Pn:function(){return o},Wi:function(){return a},frameData:function(){return l},S6:function(){return u}});var i=r(65);class n{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){let t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}let s=["prepare","read","update","preRender","render","postRender"],{schedule:a,cancel:o,state:l,steps:u}=function(e,t){let r=!1,i=!0,a={delta:0,timestamp:0,isProcessing:!1},o=s.reduce((e,t)=>(e[t]=function(e){let t=new n,r=new n,i=0,s=!1,a=!1,o=new WeakSet,l={schedule:(e,n=!1,a=!1)=>{let l=a&&s,u=l?t:r;return n&&o.add(e),u.add(e)&&l&&s&&(i=t.order.length),e},cancel:e=>{r.remove(e),o.delete(e)},process:n=>{if(s){a=!0;return}if(s=!0,[t,r]=[r,t],r.clear(),i=t.order.length)for(let r=0;r<i;r++){let i=t.order[r];i(n),o.has(i)&&(l.schedule(i),e())}s=!1,a&&(a=!1,l.process(n))}};return l}(()=>r=!0),e),{}),l=e=>o[e].process(a),u=()=>{let n=performance.now();r=!1,a.delta=i?1e3/60:Math.max(Math.min(n-a.timestamp,40),1),a.timestamp=n,a.isProcessing=!0,s.forEach(l),a.isProcessing=!1,r&&t&&(i=!1,e(u))},c=()=>{r=!0,i=!0,a.isProcessing||e(u)},h=s.reduce((e,t)=>{let i=o[t];return e[t]=(e,t=!1,n=!1)=>(r||c(),i.schedule(e,t,n)),e},{});return{schedule:h,cancel:e=>s.forEach(t=>o[t].cancel(e)),state:a,steps:o}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:i.Z,!0)},8508:function(e,t,r){"use strict";let i;r.d(t,{E:function(){return nZ}});var n,s,a=r(2784);let o=(0,a.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),l=(0,a.createContext)({});var u=r(7967),c=r(3617);let h=(0,a.createContext)({strict:!1}),d=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),p="data-"+d("framerAppearId");function f(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function m(e){return"string"==typeof e||Array.isArray(e)}function g(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}let y=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],v=["initial",...y];function b(e){return g(e.animate)||v.some(t=>m(e[t]))}function x(e){return!!(b(e)||e.variants)}function w(e){return Array.isArray(e)?e.join(" "):e}let S={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},k={};for(let e in S)k[e]={isEnabled:t=>S[e].some(e=>!!t[e])};var P=r(3791),E=r(3422);let C=(0,a.createContext)({}),L=Symbol.for("motionComponentSymbol"),j=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function T(e){if("string"!=typeof e||e.includes("-"));else if(j.indexOf(e)>-1||/[A-Z]/.test(e))return!0;return!1}let A={},O=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],R=new Set(O);function N(e,{layout:t,layoutId:r}){return R.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!A[e]||"opacity"===e)}let V=e=>!!(e&&e.getVelocity),D={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},M=O.length,$=e=>t=>"string"==typeof t&&t.startsWith(e),F=$("--"),I=$("var(--"),B=(e,t)=>t&&"number"==typeof e?t.transform(e):e,U=(e,t,r)=>Math.min(Math.max(r,e),t),_={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},W={..._,transform:e=>U(0,1,e)},z={..._,default:1},H=e=>Math.round(1e5*e)/1e5,K=/(-)?([\d]*\.?[\d])+/g,Z=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Y=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function q(e){return"string"==typeof e}let G=e=>({test:t=>q(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),J=G("deg"),X=G("%"),Q=G("px"),ee=G("vh"),et=G("vw"),er={...X,parse:e=>X.parse(e)/100,transform:e=>X.transform(100*e)},ei={..._,transform:Math.round},en={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,size:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,rotate:J,rotateX:J,rotateY:J,rotateZ:J,scale:z,scaleX:z,scaleY:z,scaleZ:z,skew:J,skewX:J,skewY:J,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:W,originX:er,originY:er,originZ:Q,zIndex:ei,fillOpacity:W,strokeOpacity:W,numOctaves:ei};function es(e,t,r,i){let{style:n,vars:s,transform:a,transformOrigin:o}=e,l=!1,u=!1,c=!0;for(let e in t){let r=t[e];if(F(e)){s[e]=r;continue}let i=en[e],h=B(r,i);if(R.has(e)){if(l=!0,a[e]=h,!c)continue;r!==(i.default||0)&&(c=!1)}else e.startsWith("origin")?(u=!0,o[e]=h):n[e]=h}if(!t.transform&&(l||i?n.transform=function(e,{enableHardwareAcceleration:t=!0,allowTransformNone:r=!0},i,n){let s="";for(let t=0;t<M;t++){let r=O[t];if(void 0!==e[r]){let t=D[r]||r;s+=`${t}(${e[r]}) `}}return t&&!e.z&&(s+="translateZ(0)"),s=s.trim(),n?s=n(e,i?"":s):r&&i&&(s="none"),s}(e.transform,r,c,i):n.transform&&(n.transform="none")),u){let{originX:e="50%",originY:t="50%",originZ:r=0}=o;n.transformOrigin=`${e} ${t} ${r}`}}let ea=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eo(e,t,r){for(let i in t)V(t[i])||N(i,r)||(e[i]=t[i])}function el(e,t,r){let i={},n=function(e,t,r){let i=e.style||{},n={};return eo(n,i,e),Object.assign(n,function({transformTemplate:e},t,r){return(0,a.useMemo)(()=>{let i=ea();return es(i,t,{enableHardwareAcceleration:!r},e),Object.assign({},i.vars,i.style)},[t])}(e,t,r)),e.transformValues?e.transformValues(n):n}(e,t,r);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=n,i}let eu=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ec(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||eu.has(e)}let eh=e=>!ec(e);try{(n=require("@emotion/is-prop-valid").default)&&(eh=e=>e.startsWith("on")?!ec(e):n(e))}catch(e){}function ed(e,t,r){return"string"==typeof e?e:Q.transform(t+r*e)}let ep={offset:"stroke-dashoffset",array:"stroke-dasharray"},ef={offset:"strokeDashoffset",array:"strokeDasharray"};function em(e,{attrX:t,attrY:r,attrScale:i,originX:n,originY:s,pathLength:a,pathSpacing:o=1,pathOffset:l=0,...u},c,h,d){if(es(e,u,c,d),h){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:p,style:f,dimensions:m}=e;p.transform&&(m&&(f.transform=p.transform),delete p.transform),m&&(void 0!==n||void 0!==s||f.transform)&&(f.transformOrigin=function(e,t,r){let i=ed(t,e.x,e.width),n=ed(r,e.y,e.height);return`${i} ${n}`}(m,void 0!==n?n:.5,void 0!==s?s:.5)),void 0!==t&&(p.x=t),void 0!==r&&(p.y=r),void 0!==i&&(p.scale=i),void 0!==a&&function(e,t,r=1,i=0,n=!0){e.pathLength=1;let s=n?ep:ef;e[s.offset]=Q.transform(-i);let a=Q.transform(t),o=Q.transform(r);e[s.array]=`${a} ${o}`}(p,a,o,l,!1)}let eg=()=>({...ea(),attrs:{}}),ey=e=>"string"==typeof e&&"svg"===e.toLowerCase();function ev(e,t,r,i){let n=(0,a.useMemo)(()=>{let r=eg();return em(r,t,{enableHardwareAcceleration:!1},ey(i),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};eo(t,e.style,e),n.style={...t,...n.style}}return n}function eb(e,{style:t,vars:r},i,n){for(let s in Object.assign(e.style,t,n&&n.getProjectionStyles(i)),r)e.style.setProperty(s,r[s])}let ex=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ew(e,t,r,i){for(let r in eb(e,t,void 0,i),t.attrs)e.setAttribute(ex.has(r)?r:d(r),t.attrs[r])}function eS(e,t){let{style:r}=e,i={};for(let n in r)(V(r[n])||t.style&&V(t.style[n])||N(n,e))&&(i[n]=r[n]);return i}function ek(e,t){let r=eS(e,t);for(let i in e)if(V(e[i])||V(t[i])){let t=-1!==O.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;r[t]=e[i]}return r}function eP(e,t,r,i={},n={}){return"function"==typeof t&&(t=t(void 0!==r?r:e.custom,i,n)),"string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t&&(t=t(void 0!==r?r:e.custom,i,n)),t}var eE=r(3105);let eC=e=>Array.isArray(e),eL=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),ej=e=>eC(e)?e[e.length-1]||0:e;function eT(e){let t=V(e)?e.get():e;return eL(t)?t.toValue():t}let eA=e=>(t,r)=>{let i=(0,a.useContext)(l),n=(0,a.useContext)(u.O),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:r},i,n,s){let a={latestValues:function(e,t,r,i){let n={},s=i(e,{});for(let e in s)n[e]=eT(s[e]);let{initial:a,animate:o}=e,l=b(e),u=x(e);t&&u&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===o&&(o=t.animate));let c=!!r&&!1===r.initial;c=c||!1===a;let h=c?o:a;if(h&&"boolean"!=typeof h&&!g(h)){let t=Array.isArray(h)?h:[h];t.forEach(t=>{let r=eP(e,t);if(!r)return;let{transitionEnd:i,transition:s,...a}=r;for(let e in a){let t=a[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let e in i)n[e]=i[e]})}return n}(i,n,s,e),renderState:t()};return r&&(a.mount=e=>r(i,e,a)),a})(e,t,i,n);return r?s():(0,eE.h)(s)};var eO=r(2972);let eR={useVisualState:eA({scrapeMotionValuesFromProps:ek,createRenderState:eg,onMount:(e,t,{renderState:r,latestValues:i})=>{eO.Wi.read(()=>{try{r.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){r.dimensions={x:0,y:0,width:0,height:0}}}),eO.Wi.render(()=>{em(r,i,{enableHardwareAcceleration:!1},ey(t.tagName),e.transformTemplate),ew(t,r)})}})},eN={useVisualState:eA({scrapeMotionValuesFromProps:eS,createRenderState:ea})};function eV(e,t,r,i={passive:!0}){return e.addEventListener(t,r,i),()=>e.removeEventListener(t,r)}let eD=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function eM(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}let e$=e=>t=>eD(t)&&e(t,eM(t));function eF(e,t,r,i){return eV(e,t,e$(r),i)}let eI=(e,t)=>r=>t(e(r)),eB=(...e)=>e.reduce(eI);function eU(e){let t=null;return()=>null===t&&(t=e,()=>{t=null})}let e_=eU("dragHorizontal"),eW=eU("dragVertical");function ez(e){let t=!1;if("y"===e)t=eW();else if("x"===e)t=e_();else{let e=e_(),r=eW();e&&r?t=()=>{e(),r()}:(e&&e(),r&&r())}return t}function eH(){let e=ez(!0);return!e||(e(),!1)}class eK{constructor(e){this.isMounted=!1,this.node=e}update(){}}function eZ(e,t){let r="onHover"+(t?"Start":"End");return eF(e.current,"pointer"+(t?"enter":"leave"),(i,n)=>{if("touch"===i.pointerType||eH())return;let s=e.getProps();e.animationState&&s.whileHover&&e.animationState.setActive("whileHover",t),s[r]&&eO.Wi.update(()=>s[r](i,n))},{passive:!e.getProps()[r]})}class eY extends eK{mount(){this.unmount=eB(eZ(this.node,!0),eZ(this.node,!1))}unmount(){}}class eq extends eK{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eB(eV(this.node.current,"focus",()=>this.onFocus()),eV(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let eG=(e,t)=>!!t&&(e===t||eG(e,t.parentElement));var eJ=r(65);function eX(e,t){if(!t)return;let r=new PointerEvent("pointer"+e);t(r,eM(r))}class eQ extends eK{constructor(){super(...arguments),this.removeStartListeners=eJ.Z,this.removeEndListeners=eJ.Z,this.removeAccessibleListeners=eJ.Z,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),i=eF(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;let{onTap:r,onTapCancel:i,globalTapTarget:n}=this.node.getProps();eO.Wi.update(()=>{n||eG(this.node.current,e.target)?r&&r(e,t):i&&i(e,t)})},{passive:!(r.onTap||r.onPointerUp)}),n=eF(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=eB(i,n),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=eV(this.node.current,"keydown",e=>{"Enter"!==e.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=eV(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&eX("up",(e,t)=>{let{onTap:r}=this.node.getProps();r&&eO.Wi.update(()=>r(e,t))})}),eX("down",(e,t)=>{this.startPress(e,t)}))}),t=eV(this.node.current,"blur",()=>{this.isPressing&&eX("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=eB(e,t)}}startPress(e,t){this.isPressing=!0;let{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&eO.Wi.update(()=>r(e,t))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;let e=this.node.getProps();return e.whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!eH()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&eO.Wi.update(()=>r(e,t))}mount(){let e=this.node.getProps(),t=eF(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),r=eV(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=eB(t,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let e0=new WeakMap,e1=new WeakMap,e2=e=>{let t=e0.get(e.target);t&&t(e)},e5=e=>{e.forEach(e2)},e3={some:0,all:1};class e4 extends eK{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:i="some",once:n}=e,s={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof i?i:e3[i]};return function(e,t,r){let i=function({root:e,...t}){let r=e||document;e1.has(r)||e1.set(r,{});let i=e1.get(r),n=JSON.stringify(t);return i[n]||(i[n]=new IntersectionObserver(e5,{root:e,...t})),i[n]}(t);return e0.set(e,r),i.observe(e),()=>{e0.delete(e),i.unobserve(e)}}(this.node.current,s,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,n&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:i}=this.node.getProps(),s=t?r:i;s&&s(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node,r=["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t));r&&this.startObserver()}unmount(){}}function e8(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let i=0;i<r;i++)if(t[i]!==e[i])return!1;return!0}function e6(e,t,r){let i=e.getProps();return eP(i,t,void 0!==r?r:i.custom,function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.get()),t}(e),function(e){let t={};return e.values.forEach((e,r)=>t[r]=e.getVelocity()),t}(e))}var e7=r(7035);let e9=e=>1e3*e,te=e=>e/1e3,tt={current:!1},tr=e=>Array.isArray(e)&&"number"==typeof e[0],ti=([e,t,r,i])=>`cubic-bezier(${e}, ${t}, ${r}, ${i})`,tn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ti([0,.65,.55,1]),circOut:ti([.55,0,1,.45]),backIn:ti([.31,.01,.66,-.59]),backOut:ti([.33,1.53,.69,.99])},ts=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function ta(e,t,r,i){if(e===t&&r===i)return eJ.Z;let n=t=>(function(e,t,r,i,n){let s,a;let o=0;do(s=ts(a=t+(r-t)/2,i,n)-e)>0?r=a:t=a;while(Math.abs(s)>1e-7&&++o<12);return a})(t,0,1,e,r);return e=>0===e||1===e?e:ts(n(e),t,i)}let to=ta(.42,0,1,1),tl=ta(0,0,.58,1),tu=ta(.42,0,.58,1),tc=e=>Array.isArray(e)&&"number"!=typeof e[0],th=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,td=e=>t=>1-e(1-t),tp=e=>1-Math.sin(Math.acos(e)),tf=td(tp),tm=th(tp),tg=ta(.33,1.53,.69,.99),ty=td(tg),tv=th(ty),tb={linear:eJ.Z,easeIn:to,easeInOut:tu,easeOut:tl,circIn:tp,circInOut:tm,circOut:tf,backIn:ty,backInOut:tv,backOut:tg,anticipate:e=>(e*=2)<1?.5*ty(e):.5*(2-Math.pow(2,-10*(e-1)))},tx=e=>{if(Array.isArray(e)){(0,e7.k)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,i,n]=e;return ta(t,r,i,n)}return"string"==typeof e?((0,e7.k)(void 0!==tb[e],`Invalid easing type '${e}'`),tb[e]):e},tw=(e,t)=>r=>!!(q(r)&&Y.test(r)&&r.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(r,t)),tS=(e,t,r)=>i=>{if(!q(i))return i;let[n,s,a,o]=i.match(K);return{[e]:parseFloat(n),[t]:parseFloat(s),[r]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},tk=e=>U(0,255,e),tP={..._,transform:e=>Math.round(tk(e))},tE={test:tw("rgb","red"),parse:tS("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:i=1})=>"rgba("+tP.transform(e)+", "+tP.transform(t)+", "+tP.transform(r)+", "+H(W.transform(i))+")"},tC={test:tw("#"),parse:function(e){let t="",r="",i="",n="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),i=e.substring(5,7),n=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),i=e.substring(3,4),n=e.substring(4,5),t+=t,r+=r,i+=i,n+=n),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(i,16),alpha:n?parseInt(n,16)/255:1}},transform:tE.transform},tL={test:tw("hsl","hue"),parse:tS("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:i=1})=>"hsla("+Math.round(e)+", "+X.transform(H(t))+", "+X.transform(H(r))+", "+H(W.transform(i))+")"},tj={test:e=>tE.test(e)||tC.test(e)||tL.test(e),parse:e=>tE.test(e)?tE.parse(e):tL.test(e)?tL.parse(e):tC.parse(e),transform:e=>q(e)?e:e.hasOwnProperty("red")?tE.transform(e):tL.transform(e)},tT=(e,t,r)=>-r*e+r*t+e;function tA(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}let tO=(e,t,r)=>{let i=e*e;return Math.sqrt(Math.max(0,r*(t*t-i)+i))},tR=[tC,tE,tL],tN=e=>tR.find(t=>t.test(e));function tV(e){let t=tN(e);(0,e7.k)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`);let r=t.parse(e);return t===tL&&(r=function({hue:e,saturation:t,lightness:r,alpha:i}){e/=360,r/=100;let n=0,s=0,a=0;if(t/=100){let i=r<.5?r*(1+t):r+t-r*t,o=2*r-i;n=tA(o,i,e+1/3),s=tA(o,i,e),a=tA(o,i,e-1/3)}else n=s=a=r;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*a),alpha:i}}(r)),r}let tD=(e,t)=>{let r=tV(e),i=tV(t),n={...r};return e=>(n.red=tO(r.red,i.red,e),n.green=tO(r.green,i.green,e),n.blue=tO(r.blue,i.blue,e),n.alpha=tT(r.alpha,i.alpha,e),tE.transform(n))},tM={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:eJ.Z},t$={regex:Z,countKey:"Colors",token:"${c}",parse:tj.parse},tF={regex:K,countKey:"Numbers",token:"${n}",parse:_.parse};function tI(e,{regex:t,countKey:r,token:i,parse:n}){let s=e.tokenised.match(t);s&&(e["num"+r]=s.length,e.tokenised=e.tokenised.replace(t,i),e.values.push(...s.map(n)))}function tB(e){let t=e.toString(),r={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&tI(r,tM),tI(r,t$),tI(r,tF),r}function tU(e){return tB(e).values}function t_(e){let{values:t,numColors:r,numVars:i,tokenised:n}=tB(e),s=t.length;return e=>{let t=n;for(let n=0;n<s;n++)t=n<i?t.replace(tM.token,e[n]):n<i+r?t.replace(t$.token,tj.transform(e[n])):t.replace(tF.token,H(e[n]));return t}}let tW=e=>"number"==typeof e?0:e,tz={test:function(e){var t,r;return isNaN(e)&&q(e)&&((null===(t=e.match(K))||void 0===t?void 0:t.length)||0)+((null===(r=e.match(Z))||void 0===r?void 0:r.length)||0)>0},parse:tU,createTransformer:t_,getAnimatableNone:function(e){let t=tU(e),r=t_(e);return r(t.map(tW))}},tH=(e,t)=>r=>`${r>0?t:e}`;function tK(e,t){return"number"==typeof e?r=>tT(e,t,r):tj.test(e)?tD(e,t):e.startsWith("var(")?tH(e,t):tq(e,t)}let tZ=(e,t)=>{let r=[...e],i=r.length,n=e.map((e,r)=>tK(e,t[r]));return e=>{for(let t=0;t<i;t++)r[t]=n[t](e);return r}},tY=(e,t)=>{let r={...e,...t},i={};for(let n in r)void 0!==e[n]&&void 0!==t[n]&&(i[n]=tK(e[n],t[n]));return e=>{for(let t in i)r[t]=i[t](e);return r}},tq=(e,t)=>{let r=tz.createTransformer(t),i=tB(e),n=tB(t),s=i.numVars===n.numVars&&i.numColors===n.numColors&&i.numNumbers>=n.numNumbers;return s?eB(tZ(i.values,n.values),r):((0,e7.K)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tH(e,t))},tG=(e,t,r)=>{let i=t-e;return 0===i?1:(r-e)/i},tJ=(e,t)=>r=>tT(e,t,r);function tX(e,t,{clamp:r=!0,ease:i,mixer:n}={}){let s=e.length;if((0,e7.k)(s===t.length,"Both input and output ranges must be the same length"),1===s)return()=>t[0];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,r){let i=[],n=r||function(e){if("number"==typeof e);else if("string"==typeof e)return tj.test(e)?tD:tq;else if(Array.isArray(e))return tZ;else if("object"==typeof e)return tY;return tJ}(e[0]),s=e.length-1;for(let r=0;r<s;r++){let s=n(e[r],e[r+1]);if(t){let e=Array.isArray(t)?t[r]||eJ.Z:t;s=eB(e,s)}i.push(s)}return i}(t,i,n),o=a.length,l=t=>{let r=0;if(o>1)for(;r<e.length-2&&!(t<e[r+1]);r++);let i=tG(e[r],e[r+1],t);return a[r](i)};return r?t=>l(U(e[0],e[s-1],t)):l}function tQ({duration:e=300,keyframes:t,times:r,ease:i="easeInOut"}){let n=tc(i)?i.map(tx):tx(i),s={done:!1,value:t[0]},a=(r&&r.length===t.length?r:function(e){let t=[0];return function(e,t){let r=e[e.length-1];for(let i=1;i<=t;i++){let n=tG(0,t,i);e.push(tT(r,1,n))}}(t,e.length-1),t}(t)).map(t=>t*e),o=tX(a,t,{ease:Array.isArray(n)?n:t.map(()=>n||tu).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=o(t),s.done=t>=e,s)}}function t0(e,t,r){var i,n;let s=Math.max(t-5,0);return i=r-e(s),(n=t-s)?i*(1e3/n):0}function t1(e,t){return e*Math.sqrt(1-t*t)}let t2=["duration","bounce"],t5=["stiffness","damping","mass"];function t3(e,t){return t.some(t=>void 0!==e[t])}function t4({keyframes:e,restDelta:t,restSpeed:r,...i}){let n;let s=e[0],a=e[e.length-1],o={done:!1,value:s},{stiffness:l,damping:u,mass:c,duration:h,velocity:d,isResolvedFromDuration:p}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!t3(e,t5)&&t3(e,t2)){let r=function({duration:e=800,bounce:t=.25,velocity:r=0,mass:i=1}){let n,s;(0,e7.K)(e<=e9(10),"Spring duration must be 10 seconds or less");let a=1-t;a=U(.05,1,a),e=U(.01,10,te(e)),a<1?(n=t=>{let i=t*a,n=i*e,s=t1(t,a);return .001-(i-r)/s*Math.exp(-n)},s=t=>{let i=t*a,s=i*e,o=Math.pow(a,2)*Math.pow(t,2)*e,l=t1(Math.pow(t,2),a),u=-n(t)+.001>0?-1:1;return u*((s*r+r-o)*Math.exp(-s))/l}):(n=t=>{let i=Math.exp(-t*e),n=(t-r)*e+1;return -.001+i*n},s=t=>{let i=Math.exp(-t*e),n=(r-t)*(e*e);return i*n});let o=5/e,l=function(e,t,r){let i=r;for(let r=1;r<12;r++)i-=e(i)/t(i);return i}(n,s,o);if(e=e9(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{let t=Math.pow(l,2)*i;return{stiffness:t,damping:2*a*Math.sqrt(i*t),duration:e}}}(e);(t={...t,...r,mass:1}).isResolvedFromDuration=!0}return t}({...i,velocity:-te(i.velocity||0)}),f=d||0,m=u/(2*Math.sqrt(l*c)),g=a-s,y=te(Math.sqrt(l/c)),v=5>Math.abs(g);if(r||(r=v?.01:2),t||(t=v?.005:.5),m<1){let e=t1(y,m);n=t=>{let r=Math.exp(-m*y*t);return a-r*((f+m*y*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}}else if(1===m)n=e=>a-Math.exp(-y*e)*(g+(f+y*g)*e);else{let e=y*Math.sqrt(m*m-1);n=t=>{let r=Math.exp(-m*y*t),i=Math.min(e*t,300);return a-r*((f+m*y*g)*Math.sinh(i)+e*g*Math.cosh(i))/e}}return{calculatedDuration:p&&h||null,next:e=>{let i=n(e);if(p)o.done=e>=h;else{let s=f;0!==e&&(s=m<1?t0(n,e,i):0);let l=Math.abs(s)<=r,u=Math.abs(a-i)<=t;o.done=l&&u}return o.value=o.done?a:i,o}}}function t8({keyframes:e,velocity:t=0,power:r=.8,timeConstant:i=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:c}){let h,d;let p=e[0],f={done:!1,value:p},m=e=>void 0!==o&&e<o||void 0!==l&&e>l,g=e=>void 0===o?l:void 0===l?o:Math.abs(o-e)<Math.abs(l-e)?o:l,y=r*t,v=p+y,b=void 0===a?v:a(v);b!==v&&(y=b-p);let x=e=>-y*Math.exp(-e/i),w=e=>b+x(e),S=e=>{let t=x(e),r=w(e);f.done=Math.abs(t)<=u,f.value=f.done?b:r},k=e=>{m(f.value)&&(h=e,d=t4({keyframes:[f.value,g(f.value)],velocity:t0(w,e,f.value),damping:n,stiffness:s,restDelta:u,restSpeed:c}))};return k(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==h||(t=!0,S(e),k(e)),void 0!==h&&e>h)?d.next(e-h):(t||S(e),f)}}}let t6=e=>{let t=({timestamp:t})=>e(t);return{start:()=>eO.Wi.update(t,!0),stop:()=>(0,eO.Pn)(t),now:()=>eO.frameData.isProcessing?eO.frameData.timestamp:performance.now()}};function t7(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}let t9={decay:t8,inertia:t8,tween:tQ,keyframes:tQ,spring:t4};function re({autoplay:e=!0,delay:t=0,driver:r=t6,keyframes:i,type:n="keyframes",repeat:s=0,repeatDelay:a=0,repeatType:o="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:h,...d}){let p,f,m,g,y,v=1,b=!1,x=()=>{f=new Promise(e=>{p=e})};x();let w=t9[n]||tQ;w!==tQ&&"number"!=typeof i[0]&&(g=tX([0,100],i,{clamp:!1}),i=[0,100]);let S=w({...d,keyframes:i});"mirror"===o&&(y=w({...d,keyframes:[...i].reverse(),velocity:-(d.velocity||0)}));let k="idle",P=null,E=null,C=null;null===S.calculatedDuration&&s&&(S.calculatedDuration=t7(S));let{calculatedDuration:L}=S,j=1/0,T=1/0;null!==L&&(T=(j=L+a)*(s+1)-a);let A=0,O=e=>{if(null===E)return;v>0&&(E=Math.min(E,e)),v<0&&(E=Math.min(e-T/v,E)),A=null!==P?P:Math.round(e-E)*v;let r=A-t*(v>=0?1:-1),n=v>=0?r<0:r>T;A=Math.max(r,0),"finished"===k&&null===P&&(A=T);let l=A,u=S;if(s){let e=Math.min(A,T)/j,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,t=Math.min(t,s+1);let i=!!(t%2);i&&("reverse"===o?(r=1-r,a&&(r-=a/j)):"mirror"===o&&(u=y)),l=U(0,1,r)*j}let c=n?{done:!1,value:i[0]}:u.next(l);g&&(c.value=g(c.value));let{done:d}=c;n||null===L||(d=v>=0?A>=T:A<=0);let p=null===P&&("finished"===k||"running"===k&&d);return h&&h(c.value),p&&V(),c},R=()=>{m&&m.stop(),m=void 0},N=()=>{k="idle",R(),p(),x(),E=C=null},V=()=>{k="finished",c&&c(),R(),p()},D=()=>{if(b)return;m||(m=r(O));let e=m.now();l&&l(),null!==P?E=e-P:E&&"finished"!==k||(E=e),"finished"===k&&x(),C=E,P=null,k="running",m.start()};e&&D();let M={then:(e,t)=>f.then(e,t),get time(){return te(A)},set time(newTime){A=newTime=e9(newTime),null===P&&m&&0!==v?E=m.now()-newTime/v:P=newTime},get duration(){let e=null===S.calculatedDuration?t7(S):S.calculatedDuration;return te(e)},get speed(){return v},set speed(newSpeed){if(newSpeed===v||!m)return;v=newSpeed,M.time=te(A)},get state(){return k},play:D,pause:()=>{k="paused",P=A},stop:()=>{b=!0,"idle"!==k&&(k="idle",u&&u(),N())},cancel:()=>{null!==C&&O(C),N()},complete:()=>{k="finished"},sample:e=>(E=0,O(e))};return M}let rt=(s=()=>Object.hasOwnProperty.call(Element.prototype,"animate"),()=>(void 0===i&&(i=s()),i)),rr=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),ri=(e,t)=>"spring"===t.type||"backgroundColor"===e||!function e(t){return!!(!t||"string"==typeof t&&tn[t]||tr(t)||Array.isArray(t)&&t.every(e))}(t.ease),rn={type:"spring",stiffness:500,damping:25,restSpeed:10},rs=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),ra={type:"keyframes",duration:.8},ro={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rl=(e,{keyframes:t})=>t.length>2?ra:R.has(e)?e.startsWith("scale")?rs(t[1]):rn:ro,ru=(e,t)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tz.test(t)||"0"===t)&&!t.startsWith("url(")),rc=new Set(["brightness","contrast","saturate","opacity"]);function rh(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[i]=r.match(K)||[];if(!i)return e;let n=r.replace(i,""),s=rc.has(t)?1:0;return i!==r&&(s*=100),t+"("+s+n+")"}let rd=/([a-z-]*)\(.*?\)/g,rp={...tz,getAnimatableNone:e=>{let t=e.match(rd);return t?t.map(rh).join(" "):e}},rf={...en,color:tj,backgroundColor:tj,outlineColor:tj,fill:tj,stroke:tj,borderColor:tj,borderTopColor:tj,borderRightColor:tj,borderBottomColor:tj,borderLeftColor:tj,filter:rp,WebkitFilter:rp},rm=e=>rf[e];function rg(e,t){let r=rm(e);return r!==rp&&(r=tz),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let ry=e=>/^0[^.\s]+$/.test(e);function rv(e,t){return e[t]||e.default||e}let rb={skipAnimations:!1},rx=(e,t,r,i={})=>n=>{let s=rv(i,e)||{},a=s.delay||i.delay||0,{elapsed:o=0}=i;o-=e9(a);let l=function(e,t,r,i){let n,s;let a=ru(t,r);n=Array.isArray(r)?[...r]:[null,r];let o=void 0!==i.from?i.from:e.get(),l=[];for(let e=0;e<n.length;e++){var u;null===n[e]&&(n[e]=0===e?o:n[e-1]),("number"==typeof(u=n[e])?0===u:null!==u?"none"===u||"0"===u||ry(u):void 0)&&l.push(e),"string"==typeof n[e]&&"none"!==n[e]&&"0"!==n[e]&&(s=n[e])}if(a&&l.length&&s)for(let e=0;e<l.length;e++){let r=l[e];n[r]=rg(t,s)}return n}(t,e,r,s),u=l[0],c=l[l.length-1],h=ru(e,u),d=ru(e,c);(0,e7.K)(h===d,`You are trying to animate ${e} from "${u}" to "${c}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${c} via the \`style\` property.`);let p={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...s,delay:-o,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{n(),s.onComplete&&s.onComplete()}};if(!function({when:e,delay:t,delayChildren:r,staggerChildren:i,staggerDirection:n,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(s)&&(p={...p,...rl(e,p)}),p.duration&&(p.duration=e9(p.duration)),p.repeatDelay&&(p.repeatDelay=e9(p.repeatDelay)),!h||!d||tt.current||!1===s.type||rb.skipAnimations)return function({keyframes:e,delay:t,onUpdate:r,onComplete:i}){let n=()=>(r&&r(e[e.length-1]),i&&i(),{time:0,speed:1,duration:0,play:eJ.Z,pause:eJ.Z,stop:eJ.Z,then:e=>(e(),Promise.resolve()),cancel:eJ.Z,complete:eJ.Z});return t?re({keyframes:[0,1],duration:0,delay:t,onComplete:n}):n()}(tt.current?{...p,delay:0}:p);if(!i.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let r=function(e,t,{onUpdate:r,onComplete:i,...n}){let s,a;let o=rt()&&rr.has(t)&&!n.repeatDelay&&"mirror"!==n.repeatType&&0!==n.damping&&"inertia"!==n.type;if(!o)return!1;let l=!1,u=!1,c=()=>{a=new Promise(e=>{s=e})};c();let{keyframes:h,duration:d=300,ease:p,times:f}=n;if(ri(t,n)){let e=re({...n,repeat:0,delay:0}),t={done:!1,value:h[0]},r=[],i=0;for(;!t.done&&i<2e4;)t=e.sample(i),r.push(t.value),i+=10;f=void 0,h=r,d=i-10,p="linear"}let m=function(e,t,r,{delay:i=0,duration:n,repeat:s=0,repeatType:a="loop",ease:o,times:l}={}){let u={[t]:r};l&&(u.offset=l);let c=function e(t){if(t)return tr(t)?ti(t):Array.isArray(t)?t.map(e):tn[t]}(o);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:i,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"})}(e.owner.current,t,h,{...n,duration:d,ease:p,times:f}),g=()=>{u=!1,m.cancel()},y=()=>{u=!0,eO.Wi.update(g),s(),c()};return m.onfinish=()=>{u||(e.set(function(e,{repeat:t,repeatType:r="loop"}){let i=t&&"loop"!==r&&t%2==1?0:e.length-1;return e[i]}(h,n)),i&&i(),y())},{then:(e,t)=>a.then(e,t),attachTimeline:e=>(m.timeline=e,m.onfinish=null,eJ.Z),get time(){return te(m.currentTime||0)},set time(newTime){m.currentTime=e9(newTime)},get speed(){return m.playbackRate},set speed(newSpeed){m.playbackRate=newSpeed},get duration(){return te(d)},play:()=>{l||(m.play(),(0,eO.Pn)(g))},pause:()=>m.pause(),stop:()=>{if(l=!0,"idle"===m.playState)return;let{currentTime:t}=m;if(t){let r=re({...n,autoplay:!1});e.setWithVelocity(r.sample(t-10).value,r.sample(t).value,10)}y()},complete:()=>{u||m.finish()},cancel:y}}(t,e,p);if(r)return r}return re(p)};function rw(e){return!!(V(e)&&e.add)}let rS=e=>/^\-?\d*\.?\d+$/.test(e);function rk(e,t){-1===e.indexOf(t)&&e.push(t)}function rP(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class rE{constructor(){this.subscriptions=[]}add(e){return rk(this.subscriptions,e),()=>rP(this.subscriptions,e)}notify(e,t,r){let i=this.subscriptions.length;if(i){if(1===i)this.subscriptions[0](e,t,r);else for(let n=0;n<i;n++){let i=this.subscriptions[n];i&&i(e,t,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let rC=e=>!isNaN(parseFloat(e)),rL={current:void 0};class rj{constructor(e,t={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(e,t=!0)=>{this.prev=this.current,this.current=e;let{delta:r,timestamp:i}=eO.frameData;this.lastUpdated!==i&&(this.timeDelta=r,this.lastUpdated=i,eO.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>eO.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:e})=>{e!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=rC(this.current),this.owner=t.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new rE);let r=this.events[e].add(t);return"change"===e?()=>{r(),eO.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=e,this.timeDelta=r}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return rL.current&&rL.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e,t;return this.canTrackVelocity?(e=parseFloat(this.current)-parseFloat(this.prev),(t=this.timeDelta)?e*(1e3/t):0):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function rT(e,t){return new rj(e,t)}let rA=e=>t=>t.test(e),rO=[_,Q,X,J,et,ee,{test:e=>"auto"===e,parse:e=>e}],rR=e=>rO.find(rA(e)),rN=[...rO,tj,tz],rV=e=>rN.find(rA(e));function rD(e,t,{delay:r=0,transitionOverride:i,type:n}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:a,...o}=e.makeTargetAnimatable(t),l=e.getValue("willChange");i&&(s=i);let u=[],c=n&&e.animationState&&e.animationState.getState()[n];for(let t in o){let i=e.getValue(t),n=o[t];if(!i||void 0===n||c&&function({protectedKeys:e,needsAnimating:t},r){let i=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,i}(c,t))continue;let a={delay:r,elapsed:0,...rv(s||{},t)};if(window.HandoffAppearAnimations){let r=e.getProps()[p];if(r){let e=window.HandoffAppearAnimations(r,t,i,eO.Wi);null!==e&&(a.elapsed=e,a.isHandoff=!0)}}let h=!a.isHandoff&&!function(e,t){let r=e.get();if(!Array.isArray(t))return r!==t;for(let e=0;e<t.length;e++)if(t[e]!==r)return!0}(i,n);if("spring"===a.type&&(i.getVelocity()||a.velocity)&&(h=!1),i.animation&&(h=!1),h)continue;i.start(rx(t,i,n,e.shouldReduceMotion&&R.has(t)?{type:!1}:a));let d=i.animation;rw(l)&&(l.add(t),d.then(()=>l.remove(t))),u.push(d)}return a&&Promise.all(u).then(()=>{a&&function(e,t){let r=e6(e,t),{transitionEnd:i={},transition:n={},...s}=r?e.makeTargetAnimatable(r,!1):{};for(let t in s={...s,...i}){let r=ej(s[t]);e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,rT(r))}}(e,a)}),u}function rM(e,t,r={}){let i=e6(e,t,r.custom),{transition:n=e.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(n=r.transitionOverride);let s=i?()=>Promise.all(rD(e,i,r)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(i=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=n;return function(e,t,r=0,i=0,n=1,s){let a=[],o=(e.variantChildren.size-1)*i,l=1===n?(e=0)=>e*i:(e=0)=>o-e*i;return Array.from(e.variantChildren).sort(r$).forEach((e,i)=>{e.notify("AnimationStart",t),a.push(rM(e,t,{...s,delay:r+l(i)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,s+i,a,o,r)}:()=>Promise.resolve(),{when:o}=n;if(!o)return Promise.all([s(),a(r.delay)]);{let[e,t]="beforeChildren"===o?[s,a]:[a,s];return e().then(()=>t())}}function r$(e,t){return e.sortNodePosition(t)}let rF=[...y].reverse(),rI=y.length;function rB(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class rU extends eK{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let i;if(e.notify("AnimationStart",t),Array.isArray(t)){let n=t.map(t=>rM(e,t,r));i=Promise.all(n)}else if("string"==typeof t)i=rM(e,t,r);else{let n="function"==typeof t?e6(e,t,r.custom):t;i=Promise.all(rD(e,n,r))}return i.then(()=>e.notify("AnimationComplete",t))})(e,t,r))),r={animate:rB(!0),whileInView:rB(),whileHover:rB(),whileTap:rB(),whileDrag:rB(),whileFocus:rB(),exit:rB()},i=!0,n=(t,r)=>{let i=e6(e,r);if(i){let{transition:e,transitionEnd:r,...n}=i;t={...t,...n,...r}}return t};function s(s,a){let o=e.getProps(),l=e.getVariantContext(!0)||{},u=[],c=new Set,h={},d=1/0;for(let t=0;t<rI;t++){var p;let f=rF[t],y=r[f],v=void 0!==o[f]?o[f]:l[f],b=m(v),x=f===a?y.isActive:null;!1===x&&(d=t);let w=v===l[f]&&v!==o[f]&&b;if(w&&i&&e.manuallyAnimateOnMount&&(w=!1),y.protectedKeys={...h},!y.isActive&&null===x||!v&&!y.prevProp||g(v)||"boolean"==typeof v)continue;let S=(p=y.prevProp,"string"==typeof v?v!==p:!!Array.isArray(v)&&!e8(v,p)),k=S||f===a&&y.isActive&&!w&&b||t>d&&b,P=!1,E=Array.isArray(v)?v:[v],C=E.reduce(n,{});!1===x&&(C={});let{prevResolvedValues:L={}}=y,j={...L,...C},T=e=>{k=!0,c.has(e)&&(P=!0,c.delete(e)),y.needsAnimating[e]=!0};for(let e in j){let t=C[e],r=L[e];if(!h.hasOwnProperty(e))(eC(t)&&eC(r)?e8(t,r):t===r)?void 0!==t&&c.has(e)?T(e):y.protectedKeys[e]=!0:void 0!==t?T(e):c.add(e)}y.prevProp=v,y.prevResolvedValues=C,y.isActive&&(h={...h,...C}),i&&e.blockInitialAnimation&&(k=!1),k&&(!w||P)&&u.push(...E.map(e=>({animation:e,options:{type:f,...s}})))}if(c.size){let t={};c.forEach(r=>{let i=e.getBaseTarget(r);void 0!==i&&(t[r]=i)}),u.push({animation:t})}let f=!!u.length;return i&&(!1===o.initial||o.initial===o.animate)&&!e.manuallyAnimateOnMount&&(f=!1),i=!1,f?t(u):Promise.resolve()}return{animateChanges:s,setActive:function(t,i,n){var a;if(r[t].isActive===i)return Promise.resolve();null===(a=e.variantChildren)||void 0===a||a.forEach(e=>{var r;return null===(r=e.animationState)||void 0===r?void 0:r.setActive(t,i)}),r[t].isActive=i;let o=s(n,t);for(let e in r)r[e].protectedKeys={};return o},setAnimateFunction:function(r){t=r(e)},getState:()=>r}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),g(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}let r_=0;class rW extends eK{constructor(){super(...arguments),this.id=r_++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let n=this.node.animationState.setActive("exit",!e,{custom:null!=r?r:this.node.getProps().custom});t&&!e&&n.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}let rz=(e,t)=>Math.abs(e-t);class rH{constructor(e,t,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rY(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){let r=rz(e.x,t.x),i=rz(e.y,t.y);return Math.sqrt(r**2+i**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:i}=e,{timestamp:n}=eO.frameData;this.history.push({...i,timestamp:n});let{onStart:s,onMove:a}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rK(t,this.transformPagePoint),eO.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:i,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=rY("pointercancel"===e.type?this.lastMoveEventInfo:rK(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,s),i&&i(e,s)},!eD(e))return;this.dragSnapToOrigin=n,this.handlers=t,this.transformPagePoint=r,this.contextWindow=i||window;let s=eM(e),a=rK(s,this.transformPagePoint),{point:o}=a,{timestamp:l}=eO.frameData;this.history=[{...o,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,rY(a,this.history)),this.removeListeners=eB(eF(this.contextWindow,"pointermove",this.handlePointerMove),eF(this.contextWindow,"pointerup",this.handlePointerUp),eF(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,eO.Pn)(this.updatePoint)}}function rK(e,t){return t?{point:t(e.point)}:e}function rZ(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rY({point:e},t){return{point:e,delta:rZ(e,rq(t)),offset:rZ(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,i=null,n=rq(e);for(;r>=0&&(i=e[r],!(n.timestamp-i.timestamp>e9(.1)));)r--;if(!i)return{x:0,y:0};let s=te(n.timestamp-i.timestamp);if(0===s)return{x:0,y:0};let a={x:(n.x-i.x)/s,y:(n.y-i.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,0)}}function rq(e){return e[e.length-1]}function rG(e){return e.max-e.min}function rJ(e,t=0,r=.01){return Math.abs(e-t)<=r}function rX(e,t,r,i=.5){e.origin=i,e.originPoint=tT(t.min,t.max,e.origin),e.scale=rG(r)/rG(t),(rJ(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=tT(r.min,r.max,e.origin)-e.originPoint,(rJ(e.translate)||isNaN(e.translate))&&(e.translate=0)}function rQ(e,t,r,i){rX(e.x,t.x,r.x,i?i.originX:void 0),rX(e.y,t.y,r.y,i?i.originY:void 0)}function r0(e,t,r){e.min=r.min+t.min,e.max=e.min+rG(t)}function r1(e,t,r){e.min=t.min-r.min,e.max=e.min+rG(t)}function r2(e,t,r){r1(e.x,t.x,r.x),r1(e.y,t.y,r.y)}function r5(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function r3(e,t){let r=t.min-e.min,i=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,i]=[i,r]),{min:r,max:i}}function r4(e,t,r){return{min:r8(e,t),max:r8(e,r)}}function r8(e,t){return"number"==typeof e?e:e[t]||0}let r6=()=>({translate:0,scale:1,origin:0,originPoint:0}),r7=()=>({x:r6(),y:r6()}),r9=()=>({min:0,max:0}),ie=()=>({x:r9(),y:r9()});function it(e){return[e("x"),e("y")]}function ir({top:e,left:t,right:r,bottom:i}){return{x:{min:t,max:r},y:{min:e,max:i}}}function ii(e){return void 0===e||1===e}function is({scale:e,scaleX:t,scaleY:r}){return!ii(e)||!ii(t)||!ii(r)}function ia(e){return is(e)||io(e)||e.z||e.rotate||e.rotateX||e.rotateY}function io(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function il(e,t,r,i,n){return void 0!==n&&(e=i+n*(e-i)),i+r*(e-i)+t}function iu(e,t=0,r=1,i,n){e.min=il(e.min,t,r,i,n),e.max=il(e.max,t,r,i,n)}function ic(e,{x:t,y:r}){iu(e.x,t.translate,t.scale,t.originPoint),iu(e.y,r.translate,r.scale,r.originPoint)}function ih(e){return Number.isInteger(e)?e:e>1.0000000000001||e<.999999999999?e:1}function id(e,t){e.min=e.min+t,e.max=e.max+t}function ip(e,t,[r,i,n]){let s=void 0!==t[n]?t[n]:.5,a=tT(e.min,e.max,s);iu(e,t[r],t[i],a,t.scale)}let im=["x","scaleX","originX"],ig=["y","scaleY","originY"];function iy(e,t){ip(e.x,t,im),ip(e.y,t,ig)}function iv(e,t){return ir(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),i=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:i.y,right:i.x}}(e.getBoundingClientRect(),t))}let ib=({current:e})=>e?e.ownerDocument.defaultView:null,ix=new WeakMap;class iw{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ie(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rH(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(eM(e,"page").point)},onStart:(e,t)=>{let{drag:r,dragPropagation:i,onDragStart:n}=this.getProps();if(r&&!i&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=ez(r),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),it(e=>{let t=this.getAxisMotionValue(e).get()||0;if(X.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let i=r.layout.layoutBox[e];if(i){let e=rG(i);t=e*(parseFloat(t)/100)}}}this.originPoint[e]=t}),n&&eO.Wi.update(()=>n(e,t),!1,!0);let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:i,onDirectionLock:n,onDrag:s}=this.getProps();if(!r&&!this.openGlobalLock)return;let{offset:a}=t;if(i&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>it(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:ib(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:i}=t;this.startAnimation(i);let{onDragEnd:n}=this.getProps();n&&eO.Wi.update(()=>n(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:i}=this.getProps();if(!r||!iS(e,i,this.currentDirection))return;let n=this.getAxisMotionValue(e),s=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:r},i){return void 0!==t&&e<t?e=i?tT(t,e,i.min):Math.max(e,t):void 0!==r&&e>r&&(e=i?tT(r,e,i.max):Math.min(e,r)),e}(s,this.constraints[e],this.elastic[e])),n.set(s)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,n=this.constraints;t&&f(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(e,{top:t,left:r,bottom:i,right:n}){return{x:r5(e.x,r,n),y:r5(e.y,t,i)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:r4(e,"left","right"),y:r4(e,"top","bottom")}}(r),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&it(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(i.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!f(t))return!1;let i=t.current;(0,e7.k)(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(e,t,r){let i=iv(e,r),{scroll:n}=t;return n&&(id(i.x,n.offset.x),id(i.y,n.offset.y)),i}(i,n.root,this.visualElement.getTransformPagePoint()),a={x:r3((e=n.layout.layoutBox).x,s.x),y:r3(e.y,s.y)};if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=ir(e))}return a}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:i,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{},l=it(a=>{if(!iS(a,t,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[a]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,u)});return Promise.all(l).then(a)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return r.start(rx(e,r,0,t))}stopAnimation(){it(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){it(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t="_drag"+e.toUpperCase(),r=this.visualElement.getProps(),i=r[t];return i||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){it(t=>{let{drag:r}=this.getProps();if(!iS(t,r,this.currentDirection))return;let{projection:i}=this.visualElement,n=this.getAxisMotionValue(t);if(i&&i.layout){let{min:r,max:s}=i.layout.layoutBox[t];n.set(e[t]-tT(r,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!f(t)||!r||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};it(e=>{let t=this.getAxisMotionValue(e);if(t){let r=t.get();i[e]=function(e,t){let r=.5,i=rG(e),n=rG(t);return n>i?r=tG(t.min,t.max-i,e.min):i>n&&(r=tG(e.min,e.max-n,t.min)),U(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),it(t=>{if(!iS(t,e,null))return;let r=this.getAxisMotionValue(t),{min:n,max:s}=this.constraints[t];r.set(tT(n,s,i[t]))})}addListeners(){if(!this.visualElement.current)return;ix.set(this.visualElement,this);let e=this.visualElement.current,t=eF(e,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),r=()=>{let{dragConstraints:e}=this.getProps();f(e)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();let s=eV(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(it(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),a&&a()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:i,dragConstraints:n,dragElastic:s,dragMomentum:a}}}function iS(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class ik extends eK{constructor(e){super(e),this.removeGroupControls=eJ.Z,this.removeListeners=eJ.Z,this.controls=new iw(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eJ.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let iP=e=>(t,r)=>{e&&eO.Wi.update(()=>e(t,r))};class iE extends eK{constructor(){super(...arguments),this.removePointerDownListener=eJ.Z}onPointerDown(e){this.session=new rH(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ib(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:iP(e),onStart:iP(t),onMove:r,onEnd:(e,t)=>{delete this.session,i&&eO.Wi.update(()=>i(e,t))}}}mount(){this.removePointerDownListener=eF(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let iC={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function iL(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ij={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!Q.test(e))return e;e=parseFloat(e)}let r=iL(e,t.target.x),i=iL(e,t.target.y);return`${r}% ${i}%`}};class iT extends a.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:i}=this.props,{projection:n}=e;Object.assign(A,iO),n&&(t.group&&t.group.add(n),r&&r.register&&i&&r.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),iC.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:i,isPresent:n}=this.props,s=r.projection;return s&&(s.isPresent=n,i||e.layoutDependency!==t||void 0===t?s.willUpdate():this.safeToRemove(),e.isPresent===n||(n?s.promote():s.relegate()||eO.Wi.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function iA(e){let[t,r]=function(){let e=(0,a.useContext)(u.O);if(null===e)return[!0,null];let{isPresent:t,onExitComplete:r,register:i}=e,n=(0,a.useId)();return(0,a.useEffect)(()=>i(n),[]),!t&&r?[!1,()=>r&&r(n)]:[!0]}(),i=(0,a.useContext)(E.p);return a.createElement(iT,{...e,layoutGroup:i,switchLayoutGroup:(0,a.useContext)(C),isPresent:t,safeToRemove:r})}let iO={borderRadius:{...ij,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ij,borderTopRightRadius:ij,borderBottomLeftRadius:ij,borderBottomRightRadius:ij,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let i=tz.parse(e);if(i.length>5)return e;let n=tz.createTransformer(e),s="number"!=typeof i[0]?1:0,a=r.x.scale*t.x,o=r.y.scale*t.y;i[0+s]/=a,i[1+s]/=o;let l=tT(a,o,.5);return"number"==typeof i[2+s]&&(i[2+s]/=l),"number"==typeof i[3+s]&&(i[3+s]/=l),n(i)}}},iR=["TopLeft","TopRight","BottomLeft","BottomRight"],iN=iR.length,iV=e=>"string"==typeof e?parseFloat(e):e,iD=e=>"number"==typeof e||Q.test(e);function iM(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let i$=iI(0,.5,tf),iF=iI(.5,.95,eJ.Z);function iI(e,t,r){return i=>i<e?0:i>t?1:r(tG(e,t,i))}function iB(e,t){e.min=t.min,e.max=t.max}function iU(e,t){iB(e.x,t.x),iB(e.y,t.y)}function i_(e,t,r,i,n){return e-=t,e=i+1/r*(e-i),void 0!==n&&(e=i+1/n*(e-i)),e}function iW(e,t,[r,i,n],s,a){!function(e,t=0,r=1,i=.5,n,s=e,a=e){if(X.test(t)){t=parseFloat(t);let e=tT(a.min,a.max,t/100);t=e-a.min}if("number"!=typeof t)return;let o=tT(s.min,s.max,i);e===s&&(o-=t),e.min=i_(e.min,t,r,o,n),e.max=i_(e.max,t,r,o,n)}(e,t[r],t[i],t[n],t.scale,s,a)}let iz=["x","scaleX","originX"],iH=["y","scaleY","originY"];function iK(e,t,r,i){iW(e.x,t,iz,r?r.x:void 0,i?i.x:void 0),iW(e.y,t,iH,r?r.y:void 0,i?i.y:void 0)}function iZ(e){return 0===e.translate&&1===e.scale}function iY(e){return iZ(e.x)&&iZ(e.y)}function iq(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function iG(e){return rG(e.x)/rG(e.y)}class iJ{constructor(){this.members=[]}add(e){rk(this.members,e),e.scheduleRender()}remove(e){if(rP(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:i}=e.options;!1===i&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function iX(e,t,r){let i="",n=e.x.translate/t.x,s=e.y.translate/t.y;if((n||s)&&(i=`translate3d(${n}px, ${s}px, 0) `),(1!==t.x||1!==t.y)&&(i+=`scale(${1/t.x}, ${1/t.y}) `),r){let{rotate:e,rotateX:t,rotateY:n}=r;e&&(i+=`rotate(${e}deg) `),t&&(i+=`rotateX(${t}deg) `),n&&(i+=`rotateY(${n}deg) `)}let a=e.x.scale*t.x,o=e.y.scale*t.y;return(1!==a||1!==o)&&(i+=`scale(${a}, ${o})`),i||"none"}let iQ=(e,t)=>e.depth-t.depth;class i0{constructor(){this.children=[],this.isDirty=!1}add(e){rk(this.children,e),this.isDirty=!0}remove(e){rP(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(iQ),this.isDirty=!1,this.children.forEach(e)}}let i1=["","X","Y","Z"],i2={visibility:"hidden"},i5=0,i3={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function i4({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:i,resetTransform:n}){return class{constructor(e={},r=null==t?void 0:t()){this.id=i5++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,i3.totalNodes=i3.resolvedTargetDeltas=i3.recalculatedProjection=0,this.nodes.forEach(i7),this.nodes.forEach(ns),this.nodes.forEach(na),this.nodes.forEach(i9),window.MotionDebug&&window.MotionDebug.record(i3)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new i0)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new rE),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(n||i)&&(this.isLayoutDirty=!0),e){let r;let i=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=performance.now(),i=({timestamp:n})=>{let s=n-r;s>=t&&((0,eO.Pn)(i),e(s-t))};return eO.Wi.read(i,!0),()=>(0,eO.Pn)(i)}(i,250),iC.hasAnimatedSinceResize&&(iC.hasAnimatedSinceResize=!1,this.nodes.forEach(nn))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:r,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||nd,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=s.getProps(),l=!this.targetLayout||!iq(this.targetLayout,i)||r,u=!t&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...rv(n,"layout"),onPlay:a,onComplete:o};(s.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||nn(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,eO.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(no),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;let e=this.isUpdateBlocked();if(e){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nt);return}this.isUpdating||this.nodes.forEach(nr),this.isUpdating=!1,this.nodes.forEach(ni),this.nodes.forEach(i8),this.nodes.forEach(i6),this.clearAllSnapshots();let t=performance.now();eO.frameData.delta=U(0,1e3/60,t-eO.frameData.timestamp),eO.frameData.timestamp=t,eO.frameData.isProcessing=!0,eO.S6.update.process(eO.frameData),eO.S6.preRender.process(eO.frameData),eO.S6.render.process(eO.frameData),eO.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(ne),this.sharedNodes.forEach(nl)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eO.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eO.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++){let t=this.path[e];t.updateScroll()}let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ie(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:i(this.instance),offset:r(this.instance)})}resetTransform(){if(!n)return;let e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!iY(this.projectionDelta),r=this.getTransformTemplate(),i=r?r(this.latestValues,""):void 0,s=i!==this.prevTransformTemplateValue;e&&(t||ia(this.latestValues)||s)&&(n(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),i=this.removeElementScroll(r);return e&&(i=this.removeTransform(i)),nm((t=i).x),nm(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return ie();let t=e.measureViewportBox(),{scroll:r}=this.root;return r&&(id(t.x,r.offset.x),id(t.y,r.offset.y)),t}removeElementScroll(e){let t=ie();iU(t,e);for(let r=0;r<this.path.length;r++){let i=this.path[r],{scroll:n,options:s}=i;if(i!==this.root&&n&&s.layoutScroll){if(n.isRoot){iU(t,e);let{scroll:r}=this.root;r&&(id(t.x,-r.offset.x),id(t.y,-r.offset.y))}id(t.x,n.offset.x),id(t.y,n.offset.y)}}return t}applyTransform(e,t=!1){let r=ie();iU(r,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];!t&&i.options.layoutScroll&&i.scroll&&i!==i.root&&iy(r,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),ia(i.latestValues)&&iy(r,i.latestValues)}return ia(this.latestValues)&&iy(r,this.latestValues),r}removeTransform(e){let t=ie();iU(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!ia(r.latestValues))continue;is(r.latestValues)&&r.updateSnapshot();let i=ie(),n=r.measurePageBox();iU(i,n),iK(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}return ia(this.latestValues)&&iK(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eO.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,r,i,n;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let a=!!this.resumingFrom||this!==s,o=!(e||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget);if(o)return;let{layout:l,layoutId:u}=this.options;if(this.layout&&(l||u)){if(this.resolvedRelativeTargetAt=eO.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ie(),this.relativeTargetOrigin=ie(),r2(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),iU(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=ie(),this.targetWithTransforms=ie()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,i=this.relativeTarget,n=this.relativeParent.target,r0(r.x,i.x,n.x),r0(r.y,i.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iU(this.target,this.layout.layoutBox),ic(this.target,this.targetDelta)):iU(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ie(),this.relativeTargetOrigin=ie(),r2(this.relativeTargetOrigin,this.target,e.target),iU(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}i3.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||is(this.parent.latestValues)||io(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),r=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(i=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===eO.frameData.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;iU(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,o=this.treeScale.y;!function(e,t,r,i=!1){let n,s;let a=r.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){s=(n=r[o]).projectionDelta;let a=n.instance;(!a||!a.style||"contents"!==a.style.display)&&(i&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iy(e,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,ic(e,s)),i&&ia(n.latestValues)&&iy(e,n.latestValues))}t.x=ih(t.x),t.y=ih(t.y)}}(this.layoutCorrected,this.treeScale,this.path,r),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox);let{target:l}=t;if(!l){this.projectionTransform&&(this.projectionDelta=r7(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=r7(),this.projectionDeltaWithTransform=r7());let u=this.projectionTransform;rQ(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=iX(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==a||this.treeScale.y!==o)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),i3.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e,t=!1){let r;let i=this.snapshot,n=i?i.latestValues:{},s={...this.latestValues},a=r7();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=ie(),l=i?i.source:void 0,u=this.layout?this.layout.source:void 0,c=l!==u,h=this.getStack(),d=!h||h.members.length<=1,p=!!(c&&!d&&!0===this.options.crossfade&&!this.path.some(nh));this.animationProgress=0,this.mixTargetDelta=t=>{let i=t/1e3;if(nu(a.x,e.x,i),nu(a.y,e.y,i),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var l,u,h,f;r2(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,f=this.relativeTargetOrigin,nc(h.x,f.x,o.x,i),nc(h.y,f.y,o.y,i),r&&(l=this.relativeTarget,u=r,l.x.min===u.x.min&&l.x.max===u.x.max&&l.y.min===u.y.min&&l.y.max===u.y.max)&&(this.isProjectionDirty=!1),r||(r=ie()),iU(r,this.relativeTarget)}c&&(this.animationValues=s,function(e,t,r,i,n,s){n?(e.opacity=tT(0,void 0!==r.opacity?r.opacity:1,i$(i)),e.opacityExit=tT(void 0!==t.opacity?t.opacity:1,0,iF(i))):s&&(e.opacity=tT(void 0!==t.opacity?t.opacity:1,void 0!==r.opacity?r.opacity:1,i));for(let n=0;n<iN;n++){let s=`border${iR[n]}Radius`,a=iM(t,s),o=iM(r,s);if(void 0===a&&void 0===o)continue;a||(a=0),o||(o=0);let l=0===a||0===o||iD(a)===iD(o);l?(e[s]=Math.max(tT(iV(a),iV(o),i),0),(X.test(o)||X.test(a))&&(e[s]+="%")):e[s]=o}(t.rotate||r.rotate)&&(e.rotate=tT(t.rotate||0,r.rotate||0,i))}(s,n,this.latestValues,i,p,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,eO.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eO.Wi.update(()=>{iC.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,r){let i=V(e)?e:rT(e);return i.start(rx("",i,1e3,r)),i.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:i,latestValues:n}=e;if(t&&r&&i){if(this!==e&&this.layout&&i&&ng(this.options.animationType,this.layout.layoutBox,i.layoutBox)){r=this.target||ie();let t=rG(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let i=rG(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+i}iU(t,r),iy(t,n),rQ(this.projectionDeltaWithTransform,this.layoutCorrected,t,n)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new iJ);let r=this.sharedNodes.get(e);r.add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let i=this.getStack();i&&i.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.rotate||r.rotateX||r.rotateY||r.rotateZ)&&(t=!0),!t)return;let i={};for(let t=0;t<i1.length;t++){let n="rotate"+i1[t];r[n]&&(i[n]=r[n],e.setStaticValue(n,0))}for(let t in e.render(),i)e.setStaticValue(t,i[t]);e.scheduleRender()}getProjectionStyles(e){var t,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return i2;let i={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=eT(null==e?void 0:e.pointerEvents)||"",i.transform=n?n(this.latestValues,""):"none",i;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eT(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!ia(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1),t}let a=s.animationValues||s.latestValues;this.applyTransformsToTarget(),i.transform=iX(this.projectionDeltaWithTransform,this.treeScale,a),n&&(i.transform=n(a,i.transform));let{x:o,y:l}=this.projectionDelta;for(let e in i.transformOrigin=`${100*o.origin}% ${100*l.origin}% 0`,s.animationValues?i.opacity=s===this?null!==(r=null!==(t=a.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:i.opacity=s===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0,A){if(void 0===a[e])continue;let{correct:t,applyTo:r}=A[e],n="none"===i.transform?a[e]:t(a[e],s);if(r){let e=r.length;for(let t=0;t<e;t++)i[r[t]]=n}else i[e]=n}return this.options.layoutId&&(i.pointerEvents=s===this?eT(null==e?void 0:e.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(nt),this.root.sharedNodes.clear()}}}function i8(e){e.updateLayout()}function i6(e){var t;let r=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:i}=e.layout,{animationType:n}=e.options,s=r.source!==e.layout.source;"size"===n?it(e=>{let i=s?r.measuredBox[e]:r.layoutBox[e],n=rG(i);i.min=t[e].min,i.max=i.min+n}):ng(n,r.layoutBox,t)&&it(i=>{let n=s?r.measuredBox[i]:r.layoutBox[i],a=rG(t[i]);n.max=n.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[i].max=e.relativeTarget[i].min+a)});let a=r7();rQ(a,t,r.layoutBox);let o=r7();s?rQ(o,e.applyTransform(i,!0),r.measuredBox):rQ(o,t,r.layoutBox);let l=!iY(a),u=!1;if(!e.resumeFrom){let i=e.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:n,layout:s}=i;if(n&&s){let a=ie();r2(a,r.layoutBox,n.layoutBox);let o=ie();r2(o,t,s.layoutBox),iq(a,o)||(u=!0),i.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=i)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:r,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function i7(e){i3.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function i9(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function ne(e){e.clearSnapshot()}function nt(e){e.clearMeasurements()}function nr(e){e.isLayoutDirty=!1}function ni(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function nn(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ns(e){e.resolveTargetDelta()}function na(e){e.calcProjection()}function no(e){e.resetRotation()}function nl(e){e.removeLeadSnapshot()}function nu(e,t,r){e.translate=tT(t.translate,0,r),e.scale=tT(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function nc(e,t,r,i){e.min=tT(t.min,r.min,i),e.max=tT(t.max,r.max,i)}function nh(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let nd={duration:.45,ease:[.4,0,.1,1]},np=e=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(e),nf=np("applewebkit/")&&!np("chrome/")?Math.round:eJ.Z;function nm(e){e.min=nf(e.min),e.max=nf(e.max)}function ng(e,t,r){return"position"===e||"preserve-aspect"===e&&!rJ(iG(t),iG(r),.2)}let ny=i4({attachResizeListener:(e,t)=>eV(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nv={current:void 0},nb=i4({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!nv.current){let e=new ny({});e.mount(window),e.setOptions({layoutScroll:!0}),nv.current=e}return nv.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position}),nx=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function nw(e,t,r=1){(0,e7.k)(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[i,n]=function(e){let t=nx.exec(e);if(!t)return[,];let[,r,i]=t;return[r,i]}(e);if(!i)return;let s=window.getComputedStyle(t).getPropertyValue(i);if(s){let e=s.trim();return rS(e)?parseFloat(e):e}return I(n)?nw(n,t,r+1):n}let nS=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),nk=e=>nS.has(e),nP=e=>Object.keys(e).some(nk),nE=e=>e===_||e===Q,nC=(e,t)=>parseFloat(e.split(", ")[t]),nL=(e,t)=>(r,{transform:i})=>{if("none"===i||!i)return 0;let n=i.match(/^matrix3d\((.+)\)$/);if(n)return nC(n[1],t);{let t=i.match(/^matrix\((.+)\)$/);return t?nC(t[1],e):0}},nj=new Set(["x","y","z"]),nT=O.filter(e=>!nj.has(e)),nA={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:nL(4,13),y:nL(5,14)};nA.translateX=nA.x,nA.translateY=nA.y;let nO=(e,t,r)=>{let i=t.measureViewportBox(),n=t.current,s=getComputedStyle(n),{display:a}=s,o={};"none"===a&&t.setStaticValue("display",e.display||"block"),r.forEach(e=>{o[e]=nA[e](i,s)}),t.render();let l=t.measureViewportBox();return r.forEach(r=>{let i=t.getValue(r);i&&i.jump(o[r]),e[r]=nA[r](l,s)}),e},nR=(e,t,r={},i={})=>{t={...t},i={...i};let n=Object.keys(t).filter(nk),s=[],a=!1,o=[];if(n.forEach(n=>{let l;let u=e.getValue(n);if(!e.hasValue(n))return;let c=r[n],h=rR(c),d=t[n];if(eC(d)){let e=d.length,t=null===d[0]?1:0;h=rR(c=d[t]);for(let r=t;r<e&&null!==d[r];r++)l?(0,e7.k)(rR(d[r])===l,"All keyframes must be of the same type"):(l=rR(d[r]),(0,e7.k)(l===h||nE(h)&&nE(l),"Keyframes must be of the same dimension as the current value"))}else l=rR(d);if(h!==l){if(nE(h)&&nE(l)){let e=u.get();"string"==typeof e&&u.set(parseFloat(e)),"string"==typeof d?t[n]=parseFloat(d):Array.isArray(d)&&l===Q&&(t[n]=d.map(parseFloat))}else(null==h?void 0:h.transform)&&(null==l?void 0:l.transform)&&(0===c||0===d)?0===c?u.set(l.transform(c)):t[n]=h.transform(d):(a||(s=function(e){let t=[];return nT.forEach(r=>{let i=e.getValue(r);void 0!==i&&(t.push([r,i.get()]),i.set(r.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),a=!0),o.push(n),i[n]=void 0!==i[n]?i[n]:t[n],u.jump(d))}}),!o.length)return{target:t,transitionEnd:i};{let r=o.indexOf("height")>=0?window.pageYOffset:null,n=nO(t,e,o);return s.length&&s.forEach(([t,r])=>{e.getValue(t).set(r)}),e.render(),P.j&&null!==r&&window.scrollTo({top:r}),{target:n,transitionEnd:i}}},nN=(e,t,r,i)=>{var n,s;let a=function(e,{...t},r){let i=e.current;if(!(i instanceof Element))return{target:t,transitionEnd:r};for(let n in r&&(r={...r}),e.values.forEach(e=>{let t=e.get();if(!I(t))return;let r=nw(t,i);r&&e.set(r)}),t){let e=t[n];if(!I(e))continue;let s=nw(e,i);s&&(t[n]=s,r||(r={}),void 0===r[n]&&(r[n]=e))}return{target:t,transitionEnd:r}}(e,t,i);return t=a.target,i=a.transitionEnd,n=t,s=i,nP(n)?nR(e,n,r,s):{target:n,transitionEnd:s}},nV={current:null},nD={current:!1},nM=new WeakMap,n$=Object.keys(k),nF=n$.length,nI=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],nB=v.length;class nU{constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:i,visualState:n},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>eO.Wi.render(this.render,!1,!0);let{latestValues:a,renderState:o}=n;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=o,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=b(t),this.isVariantNode=x(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(t,{});for(let e in u){let t=u[e];void 0!==a[e]&&V(t)&&(t.set(a[e],!1),rw(l)&&l.add(e))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,nM.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),nD.current||function(){if(nD.current=!0,P.j){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>nV.current=e.matches;e.addListener(t),t()}else nV.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nV.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in nM.delete(this.current),this.projection&&this.projection.unmount(),(0,eO.Pn)(this.notifyUpdate),(0,eO.Pn)(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){let r=R.has(e),i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eO.Wi.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),n=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{i(),n()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures({children:e,...t},r,i,n){let s,a;for(let e=0;e<nF;e++){let r=n$[e],{isEnabled:i,Feature:n,ProjectionNode:o,MeasureLayout:l}=k[r];o&&(s=o),i(t)&&(!this.features[r]&&n&&(this.features[r]=new n(this)),l&&(a=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);let{layoutId:e,layout:r,drag:i,dragConstraints:a,layoutScroll:o,layoutRoot:l}=t;this.projection.setOptions({layoutId:e,layout:r,alwaysMeasureLayout:!!i||a&&f(a),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,layoutScroll:o,layoutRoot:l})}return a}updateFeatures(){for(let e in this.features){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ie()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<nI.length;t++){let r=nI[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let i=e["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(e,t,r){let{willChange:i}=t;for(let n in t){let s=t[n],a=r[n];if(V(s))e.addValue(n,s),rw(i)&&i.add(n);else if(V(a))e.addValue(n,rT(s,{owner:e})),rw(i)&&i.remove(n);else if(a!==s){if(e.hasValue(n)){let t=e.getValue(n);t.hasAnimated||t.set(s)}else{let t=e.getStaticValue(n);e.addValue(n,rT(void 0!==t?t:s,{owner:e}))}}}for(let i in r)void 0===t[i]&&e.removeValue(i);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}let t={};for(let e=0;e<nB;e++){let r=v[e],i=this.props[r];(m(i)||!1===i)&&(t[r]=i)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=rT(t,{owner:this}),this.addValue(e,r)),r}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let{initial:r}=this.props,i="string"==typeof r||"object"==typeof r?null===(t=eP(this.props,r))||void 0===t?void 0:t[e]:void 0;if(r&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||V(n)?void 0!==this.initialValues[e]&&void 0===i?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new rE),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class n_ extends nU{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...r},{transformValues:i},n){let s=function(e,t,r){let i={};for(let n in e){let e=function(e,t){if(!t)return;let r=t[e]||t.default||t;return r.from}(n,t);if(void 0!==e)i[n]=e;else{let e=r.getValue(n);e&&(i[n]=e.get())}}return i}(r,e||{},this);if(i&&(t&&(t=i(t)),r&&(r=i(r)),s&&(s=i(s))),n){!function(e,t,r){var i,n;let s=Object.keys(t).filter(t=>!e.hasValue(t)),a=s.length;if(a)for(let o=0;o<a;o++){let a=s[o],l=t[a],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(n=null!==(i=r[a])&&void 0!==i?i:e.readValue(a))&&void 0!==n?n:t[a]),null!=u&&("string"==typeof u&&(rS(u)||ry(u))?u=parseFloat(u):!rV(u)&&tz.test(l)&&(u=rg(a,l)),e.addValue(a,rT(u,{owner:e})),void 0===r[a]&&(r[a]=u),null!==u&&e.setBaseTarget(a,u))}}(this,r,s);let e=nN(this,r,s,t);t=e.transitionEnd,r=e.target}return{transition:e,transitionEnd:t,...r}}}class nW extends n_{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(R.has(t)){let e=rm(t);return e&&e.default||0}{let r=window.getComputedStyle(e),i=(F(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:t}){return iv(e,t)}build(e,t,r,i){es(e,t,r,i.transformTemplate)}scrapeMotionValuesFromProps(e,t){return eS(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;V(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}renderInstance(e,t,r,i){eb(e,t,r,i)}}class nz extends n_{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(R.has(t)){let e=rm(t);return e&&e.default||0}return t=ex.has(t)?t:d(t),e.getAttribute(t)}measureInstanceViewportBox(){return ie()}scrapeMotionValuesFromProps(e,t){return ek(e,t)}build(e,t,r,i){em(e,t,r,this.isSVGTag,i.transformTemplate)}renderInstance(e,t,r,i){ew(e,t,r,i)}mount(e){this.isSVGTag=ey(e.tagName),super.mount(e)}}let nH=(e,t)=>T(e)?new nz(t,{enableHardwareAcceleration:!1}):new nW(t,{enableHardwareAcceleration:!0}),nK={animation:{Feature:rU},exit:{Feature:rW},inView:{Feature:e4},tap:{Feature:eQ},focus:{Feature:eq},hover:{Feature:eY},pan:{Feature:iE},drag:{Feature:ik,ProjectionNode:nb,MeasureLayout:iA},layout:{ProjectionNode:nb,MeasureLayout:iA}},nZ=function(e){function t(t,r={}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:i,Component:n}){e&&function(e){for(let t in e)k[t]={...k[t],...e[t]}}(e);let s=(0,a.forwardRef)(function(s,d){var g;let y;let v={...(0,a.useContext)(o),...s,layoutId:function({layoutId:e}){let t=(0,a.useContext)(E.p).id;return t&&void 0!==e?t+"-"+e:e}(s)},{isStatic:x}=v,S=function(e){let{initial:t,animate:r}=function(e,t){if(b(e)){let{initial:t,animate:r}=e;return{initial:!1===t||m(t)?t:void 0,animate:m(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,a.useContext)(l));return(0,a.useMemo)(()=>({initial:t,animate:r}),[w(t),w(r)])}(s),k=i(s,x);if(!x&&P.j){S.visualElement=function(e,t,r,i){let{visualElement:n}=(0,a.useContext)(l),s=(0,a.useContext)(h),d=(0,a.useContext)(u.O),f=(0,a.useContext)(o).reducedMotion,m=(0,a.useRef)();i=i||s.renderer,!m.current&&i&&(m.current=i(e,{visualState:t,parent:n,props:r,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:f}));let g=m.current;(0,a.useInsertionEffect)(()=>{g&&g.update(r,d)});let y=(0,a.useRef)(!!(r[p]&&!window.HandoffComplete));return(0,c.L)(()=>{g&&(g.render(),y.current&&g.animationState&&g.animationState.animateChanges())}),(0,a.useEffect)(()=>{g&&(g.updateFeatures(),!y.current&&g.animationState&&g.animationState.animateChanges(),y.current&&(y.current=!1,window.HandoffComplete=!0))}),g}(n,k,v,t);let r=(0,a.useContext)(C),i=(0,a.useContext)(h).strict;S.visualElement&&(y=S.visualElement.loadFeatures(v,i,e,r))}return a.createElement(l.Provider,{value:S},y&&S.visualElement?a.createElement(y,{visualElement:S.visualElement,...v}):null,r(n,s,(g=S.visualElement,(0,a.useCallback)(e=>{e&&k.mount&&k.mount(e),g&&(e?g.mount(e):g.unmount()),d&&("function"==typeof d?d(e):f(d)&&(d.current=e))},[g])),k,x,S.visualElement))});return s[L]=n,s}(e(t,r))}if("undefined"==typeof Proxy)return t;let r=new Map;return new Proxy(t,{get:(e,i)=>(r.has(i)||r.set(i,t(i)),r.get(i))})}((e,t)=>(function(e,{forwardMotionProps:t=!1},r,i){let n=T(e)?eR:eN;return{...n,preloadedFeatures:r,useRender:function(e=!1){return(t,r,i,{latestValues:n},s)=>{let o=T(t)?ev:el,l=o(r,n,s,t),u=function(e,t,r){let i={};for(let n in e)("values"!==n||"object"!=typeof e.values)&&(eh(n)||!0===r&&ec(n)||!t&&!ec(n)||e.draggable&&n.startsWith("onDrag"))&&(i[n]=e[n]);return i}(r,"string"==typeof t,e),c={...u,...l,ref:i},{children:h}=r,d=(0,a.useMemo)(()=>V(h)?h.get():h,[h]);return(0,a.createElement)(t,{...c,children:d})}}(t),createVisualElement:i,Component:e}})(e,t,nK,nH))},7035:function(e,t,r){"use strict";r.d(t,{K:function(){return n},k:function(){return s}});var i=r(65);let n=i.Z,s=i.Z},3791:function(e,t,r){"use strict";r.d(t,{j:function(){return i}});let i="undefined"!=typeof document},65:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});let i=e=>e},3105:function(e,t,r){"use strict";r.d(t,{h:function(){return n}});var i=r(2784);function n(e){let t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}},3617:function(e,t,r){"use strict";r.d(t,{L:function(){return s}});var i=r(2784),n=r(3791);let s=n.j?i.useLayoutEffect:i.useEffect},2202:function(e,t,r){"use strict";let i,n;r.d(t,{x7:function(){return eh},ZP:function(){return ed}});var s,a=r(2784);let o={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||o,u=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,h=/\n+/g,d=(e,t)=>{let r="",i="",n="";for(let s in e){let a=e[s];"@"==s[0]?"i"==s[1]?r=s+" "+a+";":i+="f"==s[1]?d(a,s):s+"{"+d(a,"k"==s[1]?"":t)+"}":"object"==typeof a?i+=d(a,t?t.replace(/([^,])+/g,e=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):s):null!=a&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=d.p?d.p(s,a):s+":"+a+";")}return r+(t&&n?t+"{"+n+"}":n)+i},p={},f=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+f(e[r]);return t}return e},m=(e,t,r,i,n)=>{var s;let a=f(e),o=p[a]||(p[a]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(a));if(!p[o]){let t=a!==e?e:(e=>{let t,r,i=[{}];for(;t=u.exec(e.replace(c,""));)t[4]?i.shift():t[3]?(r=t[3].replace(h," ").trim(),i.unshift(i[0][r]=i[0][r]||{})):i[0][t[1]]=t[2].replace(h," ").trim();return i[0]})(e);p[o]=d(n?{["@keyframes "+o]:t}:t,r?"":"."+o)}let l=r&&p.g?p.g:null;return r&&(p.g=p[o]),s=p[o],l?t.data=t.data.replace(l,s):-1===t.data.indexOf(s)&&(t.data=i?s+t.data:t.data+s),o},g=(e,t,r)=>e.reduce((e,i,n)=>{let s=t[n];if(s&&s.call){let e=s(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;s=t?"."+t:e&&"object"==typeof e?e.props?"":d(e,""):!1===e?"":e}return e+i+(null==s?"":s)},"");function y(e){let t=this||{},r=e.call?e(t.p):e;return m(r.unshift?r.raw?g(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}y.bind({g:1});let v,b,x,w=y.bind({k:1});function S(e,t){let r=this||{};return function(){let i=arguments;function n(s,a){let o=Object.assign({},s),l=o.className||n.className;r.p=Object.assign({theme:b&&b()},o),r.o=/ *go\d+/.test(l),o.className=y.apply(r,i)+(l?" "+l:""),t&&(o.ref=a);let u=e;return e[0]&&(u=o.as||e,delete o.as),x&&u[0]&&x(o),v(u,o)}return t?t(n):n}}var k=e=>"function"==typeof e,P=(e,t)=>k(e)?e(t):e,E=(i=0,()=>(++i).toString()),C=()=>{if(void 0===n&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");n=!e||e.matches}return n},L=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return L(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:i}=t;return{...e,toasts:e.toasts.map(e=>e.id===i||void 0===i?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let n=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+n}))}}},j=[],T={toasts:[],pausedAt:void 0},A=e=>{T=L(T,e),j.forEach(e=>{e(T)})},O={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},R=(e={})=>{let[t,r]=(0,a.useState)(T),i=(0,a.useRef)(T);(0,a.useEffect)(()=>(i.current!==T&&r(T),j.push(r),()=>{let e=j.indexOf(r);e>-1&&j.splice(e,1)}),[]);let n=t.toasts.map(t=>{var r,i,n;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(i=e[t.type])?void 0:i.duration)||(null==e?void 0:e.duration)||O[t.type],style:{...e.style,...null==(n=e[t.type])?void 0:n.style,...t.style}}});return{...t,toasts:n}},N=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||E()}),V=e=>(t,r)=>{let i=N(t,e,r);return A({type:2,toast:i}),i.id},D=(e,t)=>V("blank")(e,t);D.error=V("error"),D.success=V("success"),D.loading=V("loading"),D.custom=V("custom"),D.dismiss=e=>{A({type:3,toastId:e})},D.remove=e=>A({type:4,toastId:e}),D.promise=(e,t,r)=>{let i=D.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let n=t.success?P(t.success,e):void 0;return n?D.success(n,{id:i,...r,...null==r?void 0:r.success}):D.dismiss(i),e}).catch(e=>{let n=t.error?P(t.error,e):void 0;n?D.error(n,{id:i,...r,...null==r?void 0:r.error}):D.dismiss(i)}),e};var M=(e,t)=>{A({type:1,toast:{id:e,height:t}})},$=()=>{A({type:5,time:Date.now()})},F=new Map,I=1e3,B=(e,t=I)=>{if(F.has(e))return;let r=setTimeout(()=>{F.delete(e),A({type:4,toastId:e})},t);F.set(e,r)},U=e=>{let{toasts:t,pausedAt:r}=R(e);(0,a.useEffect)(()=>{if(r)return;let e=Date.now(),i=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&D.dismiss(t.id);return}return setTimeout(()=>D.dismiss(t.id),r)});return()=>{i.forEach(e=>e&&clearTimeout(e))}},[t,r]);let i=(0,a.useCallback)(()=>{r&&A({type:6,time:Date.now()})},[r]),n=(0,a.useCallback)((e,r)=>{let{reverseOrder:i=!1,gutter:n=8,defaultPosition:s}=r||{},a=t.filter(t=>(t.position||s)===(e.position||s)&&t.height),o=a.findIndex(t=>t.id===e.id),l=a.filter((e,t)=>t<o&&e.visible).length;return a.filter(e=>e.visible).slice(...i?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+n,0)},[t]);return(0,a.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)B(e.id,e.removeDelay);else{let t=F.get(e.id);t&&(clearTimeout(t),F.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:M,startPause:$,endPause:i,calculateOffset:n}}},_=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,W=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,z=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,H=S("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${_} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${W} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${z} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,K=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Z=S("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${K} 1s linear infinite;
`,Y=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,q=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,G=S("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Y} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${q} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,J=S("div")`
  position: absolute;
`,X=S("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Q=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=S("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Q} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:i}=e;return void 0!==t?"string"==typeof t?a.createElement(ee,null,t):t:"blank"===r?null:a.createElement(X,null,a.createElement(Z,{...i}),"loading"!==r&&a.createElement(J,null,"error"===r?a.createElement(H,{...i}):a.createElement(G,{...i})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ei=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,en=S("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,es=S("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ea=(e,t)=>{let r=e.includes("top")?1:-1,[i,n]=C()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),ei(r)];return{animation:t?`${w(i)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},eo=a.memo(({toast:e,position:t,style:r,children:i})=>{let n=e.height?ea(e.position||t||"top-center",e.visible):{opacity:0},s=a.createElement(et,{toast:e}),o=a.createElement(es,{...e.ariaProps},P(e.message,e));return a.createElement(en,{className:e.className,style:{...n,...r,...e.style}},"function"==typeof i?i({icon:s,message:o}):a.createElement(a.Fragment,null,s,o))});s=a.createElement,d.p=void 0,v=s,b=void 0,x=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:i,children:n})=>{let s=a.useCallback(t=>{if(t){let r=()=>{i(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,i]);return a.createElement("div",{ref:s,className:t,style:r},n)},eu=(e,t)=>{let r=e.includes("top"),i=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:C()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...i}},ec=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eh=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:i,children:n,containerStyle:s,containerClassName:o})=>{let{toasts:l,handlers:u}=U(r);return a.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...s},className:o,onMouseEnter:u.startPause,onMouseLeave:u.endPause},l.map(r=>{let s=r.position||t,o=eu(s,u.calculateOffset(r,{reverseOrder:e,gutter:i,defaultPosition:t}));return a.createElement(el,{id:r.id,key:r.id,onHeightUpdate:u.updateHeight,className:r.visible?ec:"",style:o},"custom"===r.type?P(r.message,r):n?n(r):a.createElement(eo,{toast:r,position:s}))}))},ed=D}},function(e){var t=function(t){return e(e.s=t)};e.O(0,[774,179],function(){return t(6570),t(5123)}),_N_E=e.O()}]);