import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useState, useEffect } from 'react';
import { useTheme } from '@/themes';
import {
  UserGroupIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  ChatBubbleLeftRightIcon,
  CurrencyDollarIcon,
  CheckBadgeIcon,
  SparklesIcon,
  RocketLaunchIcon,
} from '@heroicons/react/24/outline';

export default function Features() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const { isGoldTheme } = useTheme();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Icon mapping
  const iconMap = {
    users: UserGroupIcon,
    cpu: CpuChipIcon,
    shield: ShieldCheckIcon,
    support: ChatBubbleLeftRightIcon,
    currency: CurrencyDollarIcon,
    badge: CheckBadgeIcon,
  };

  // Get features from translation
  const features = Array.from({ length: 6 }, (_, i) => ({
    title: t(`features.items.${i}.title`),
    description: t(`features.items.${i}.description`),
    icon: t(`features.items.${i}.icon`) as keyof typeof iconMap,
  }));

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  // Theme-aware background
  const getThemeBackground = () => {
    if (isGoldTheme) {
      return `
        radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(255, 215, 0, 0.06) 0%, transparent 60%),
        linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
      `;
    } else {
      return `
        radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(217, 70, 239, 0.06) 0%, transparent 60%),
        linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #334155 75%, #475569 100%)
      `;
    }
  };

  return (
    <section
      id="features"
      ref={ref}
      className="relative section-padding overflow-hidden"
      style={{
        background: getThemeBackground()
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Theme-aware Glass Orbs */}
        <motion.div
          animate={{
            y: [-20, 20, -20],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 18, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-20 right-10 w-28 h-28 rounded-full opacity-15"
          style={{
            background: isGoldTheme ? 'rgba(255, 215, 0, 0.1)' : 'rgba(217, 70, 239, 0.1)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            border: isGoldTheme ? '1px solid rgba(255, 215, 0, 0.2)' : '1px solid rgba(217, 70, 239, 0.2)',
            boxShadow: isGoldTheme ? '0 8px 32px rgba(255, 215, 0, 0.1)' : '0 8px 32px rgba(217, 70, 239, 0.1)'
          }}
        />

        <motion.div
          animate={{
            y: [30, -30, 30],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 22, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-32 left-16 w-36 h-36 rounded-full opacity-12"
          style={{
            background: isGoldTheme ? 'rgba(184, 134, 11, 0.1)' : 'rgba(162, 28, 175, 0.1)',
            backdropFilter: 'blur(25px)',
            WebkitBackdropFilter: 'blur(25px)',
            border: isGoldTheme ? '1px solid rgba(184, 134, 11, 0.2)' : '1px solid rgba(162, 28, 175, 0.2)',
            boxShadow: isGoldTheme ? '0 8px 32px rgba(184, 134, 11, 0.1)' : '0 8px 32px rgba(162, 28, 175, 0.1)'
          }}
        />

        {/* Floating Particles */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-15, 15, -15],
              x: [-8, 8, -8],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 6 + i * 1.5,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.4
            }}
            className={`absolute w-1.5 h-1.5 rounded-full ${isGoldTheme ? 'bg-gold-400/30' : 'bg-purple-400/30'}`}
            style={{
              left: `${15 + (i * 10)}%`,
              top: `${25 + (i * 8)}%`,
              filter: 'blur(0.5px)'
            }}
          />
        ))}
      </div>
      <div className="container mx-auto container-padding relative z-10">
        {/* Enhanced Section Header with Glass Effect */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mb-20"
        >
          <motion.div
            variants={itemVariants}
            className="relative mb-8"
          >
            <motion.h2
              className={`heading-lg mb-6 relative z-10 px-8 py-4 text-arabic-premium ${isGoldTheme ? 'text-theme-gradient' : 'text-white'}`}
              initial={{ opacity: 0, y: 30, filter: 'blur(10px)' }}
              animate={{ opacity: 1, y: 0, filter: 'blur(0px)' }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
            >
              {t('features.title')}
            </motion.h2>

            {/* Glass backdrop for title */}
            <div
              className="absolute inset-0 -m-4 rounded-2xl opacity-25"
              style={{
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%)',
                backdropFilter: 'blur(30px)',
                WebkitBackdropFilter: 'blur(30px)',
                border: '1px solid rgba(255, 255, 255, 0.12)',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.08)'
              }}
            />
          </motion.div>

          <motion.p
            variants={itemVariants}
            className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed text-arabic px-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {t('features.subtitle')}
          </motion.p>
        </motion.div>

        {/* Enhanced Features Grid with Glass Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20"
        >
          {features.map((feature, index) => {
            const IconComponent = iconMap[feature.icon];

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: 0.6 + (index * 0.1),
                  type: 'spring',
                  stiffness: 100
                }}
                whileHover={{
                  scale: 1.05,
                  y: -8,
                  transition: { duration: 0.2 }
                }}
                className={`text-center p-8 group cursor-pointer ${isGoldTheme ? 'card-premium' : 'card-theme'}`}
              >
                <motion.div
                  className="flex items-center justify-center mb-6"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="w-20 h-20 rounded-3xl flex items-center justify-center relative overflow-hidden"
                    style={{
                      background: isGoldTheme
                        ? (index % 3 === 0
                          ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 215, 0, 0.1) 100%)'
                          : index % 3 === 1
                          ? 'linear-gradient(135deg, rgba(184, 134, 11, 0.2) 0%, rgba(184, 134, 11, 0.1) 100%)'
                          : 'linear-gradient(135deg, rgba(218, 165, 32, 0.2) 0%, rgba(218, 165, 32, 0.1) 100%)')
                        : (index % 3 === 0
                          ? 'linear-gradient(135deg, rgba(217, 70, 239, 0.2) 0%, rgba(217, 70, 239, 0.1) 100%)'
                          : index % 3 === 1
                          ? 'linear-gradient(135deg, rgba(162, 28, 175, 0.2) 0%, rgba(162, 28, 175, 0.1) 100%)'
                          : 'linear-gradient(135deg, rgba(139, 69, 19, 0.2) 0%, rgba(139, 69, 19, 0.1) 100%)'),
                      backdropFilter: 'blur(15px)',
                      WebkitBackdropFilter: 'blur(15px)',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <IconComponent className="w-10 h-10 text-white group-hover:scale-110 transition-transform duration-300" />

                    {/* Shimmer effect */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                      style={{
                        background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)',
                        backgroundSize: '200% 100%',
                        animation: 'glassShimmer 2s ease-in-out infinite'
                      }}
                    />
                  </div>
                </motion.div>

                <motion.h3
                  className="heading-sm mb-4 text-white group-hover:scale-105 transition-transform duration-300 text-arabic-premium"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.8 + (index * 0.1) }}
                >
                  {feature.title}
                </motion.h3>

                <motion.p
                  className="text-white/80 leading-relaxed text-arabic"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.9 + (index * 0.1) }}
                >
                  {feature.description}
                </motion.p>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Enhanced CTA with Glass Effect */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center"
        >
          <div className={`p-12 lg:p-16 relative overflow-hidden ${isGoldTheme ? 'card-premium' : 'card-theme'}`}>
            {/* Theme-aware accent elements */}
            <div className="absolute top-4 right-4 flex space-x-1 rtl:space-x-reverse opacity-30">
              <div className={`w-2 h-8 rounded ${isGoldTheme ? 'bg-gold-500' : 'bg-purple-500'}`}></div>
              <div className={`w-2 h-8 rounded ${isGoldTheme ? 'bg-gold-600' : 'bg-purple-600'}`}></div>
            </div>

            <motion.h3
              className="heading-md mb-6 text-white text-arabic-premium"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              {isRTL ? 'جاهز للبدء؟' : 'Ready to Get Started?'}
            </motion.h3>

            <motion.p
              className="text-xl text-white/90 mb-10 max-w-2xl mx-auto leading-relaxed text-arabic"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              {isRTL
                ? 'انضم إلى آلاف الخبراء والعملاء الذين يثقون بمنصتنا لتحقيق أهدافهم المهنية'
                : 'Join thousands of experts and clients who trust our platform to achieve their professional goals'
              }
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row items-center justify-center gap-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <motion.button
                type="button"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                className={`text-white text-lg font-bold px-10 py-4 w-full sm:w-auto inline-flex items-center justify-center gap-3 group text-arabic-premium ${isGoldTheme ? 'glass-button' : 'btn-theme-primary'}`}
              >
                <RocketLaunchIcon className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
                {isRTL ? 'ابدأ كخبير' : 'Start as Expert'}
                <SparklesIcon className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
              </motion.button>

              <motion.button
                type="button"
                whileHover={{ scale: 1.02, y: -1 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                className={`text-white/95 text-lg font-semibold px-8 py-4 w-full sm:w-auto flex items-center justify-center gap-3 group text-arabic ${isGoldTheme ? 'glass-button' : 'btn-theme-secondary'}`}
              >
                <UserGroupIcon className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                {isRTL ? 'ابحث عن خبير' : 'Find an Expert'}
              </motion.button>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
