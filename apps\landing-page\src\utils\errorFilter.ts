/**
 * Filter out known browser extension errors that we can't control
 * These errors are from browser extensions and don't affect our application
 */

// Store original console methods
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Extension error patterns to filter out
const EXTENSION_ERROR_PATTERNS = [
  /Frame with ID \d+ was removed/,
  /Could not establish connection\. Receiving end does not exist/,
  /MetaMask extension not found/,
  /chrome-extension:/,
  /contentscript/,
  /serviceWorker/,
  /background\.js/,
  /No tab with id:/,
  /Unchecked runtime\.lastError/,
  /\[ChromeTransport\]/,
];

/**
 * Check if an error message matches known extension error patterns
 */
function isExtensionError(message: string): boolean {
  return EXTENSION_ERROR_PATTERNS.some(pattern => pattern.test(message));
}

/**
 * Initialize error filtering to suppress extension errors in console
 */
export function initializeErrorFilter(): void {
  // Only filter in development mode
  if (process.env.NODE_ENV === 'development') {
    console.error = (...args: any[]) => {
      const message = args.join(' ');
      if (!isExtensionError(message)) {
        originalConsoleError.apply(console, args);
      }
    };

    console.warn = (...args: any[]) => {
      const message = args.join(' ');
      if (!isExtensionError(message)) {
        originalConsoleWarn.apply(console, args);
      }
    };

    // Filter unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const message = event.reason?.message || event.reason?.toString() || '';
      if (isExtensionError(message)) {
        event.preventDefault();
      }
    });

    // Filter global errors
    window.addEventListener('error', (event) => {
      const message = event.message || '';
      if (isExtensionError(message)) {
        event.preventDefault();
      }
    });
  }
}

/**
 * Restore original console methods (for testing or cleanup)
 */
export function restoreConsole(): void {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
}
